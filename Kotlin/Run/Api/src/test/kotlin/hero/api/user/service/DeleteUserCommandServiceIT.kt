package hero.api.user.service

import hero.baseutils.minusMinutes
import hero.exceptions.http.ForbiddenException
import hero.model.CancelledByRole
import hero.model.DeletedReason
import hero.model.ModeratorPermission
import hero.model.ModeratorPermission.DELETE_USERS
import hero.model.Role
import hero.model.SignInProvider
import hero.model.SupportCounts
import hero.model.UserStateChange
import hero.model.UserStateChanged
import hero.model.UserStatus
import hero.model.topics.EmailPublished
import hero.model.topics.UserDeleted
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import hero.test.TestRepositories
import hero.test.logging.TestLogger
import hero.test.time.TestClock
import io.mockk.Ordering
import io.mockk.called
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant

class DeleteUserCommandServiceIT : IntegrationTest() {
    @Test
    fun `shouldn't do anything if the user was already deleted`() {
        val underTest = DeleteUserCommandService(
            TestCollections.usersCollection,
            TestRepositories.sessionRepository,
            pubSubMock,
            logger = TestLogger,
        )
        testHelper.createUser("admin", role = Role.MODERATOR)
        val user = testHelper.createUser("themag", status = UserStatus.DELETED)
        val session = testHelper.createSession("themag")

        underTest.execute(
            DeleteUser(
                userId = "themag",
                requesterId = "admin",
                sessionId = session.id,
                cancelSubscriptions = false,
                refundSubscriptions = false,
                deletedReason = DeletedReason.LEAVING_HH,
                deletedNote = "Going to Forendors",
            ),
        )

        // nothing has changed
        assertThat(TestCollections.usersCollection[user.id].get()).isEqualTo(user)
        verify { pubSubMock wasNot called }
    }

    @Test
    fun `user (with an email) should delete his own account - and his email should be updated`() {
        val testClock = TestClock(Instant.parse("2024-12-13T17:30:30Z"))
        val underTest = DeleteUserCommandService(
            TestCollections.usersCollection,
            TestRepositories.sessionRepository,
            pubSubMock,
            clock = testClock,
            logger = TestLogger,
        )
        val user = testHelper.createUser("themag", email = "<EMAIL>", status = UserStatus.ACTIVE, language = "cs")
        val session = testHelper.createSession("themag")

        underTest.execute(
            DeleteUser(
                userId = "themag",
                requesterId = "themag",
                sessionId = session.id,
                cancelSubscriptions = false,
                refundSubscriptions = false,
                deletedReason = DeletedReason.LEAVING_HH,
                deletedNote = "Going to Forendors",
            ),
        )

        val expectedUser = user.copy(
            status = UserStatus.DELETED,
            email = "<EMAIL>-2024-12-13-1734111030",
            deletedAt = Instant.parse("2024-12-13T17:30:30Z"),
            deletedReason = DeletedReason.LEAVING_HH,
            deletedNote = "Going to Forendors",
        )

        assertThat(TestCollections.usersCollection[user.id].get()).isEqualTo(expectedUser)
        verify(Ordering.ORDERED) {
            pubSubMock.publish(
                EmailPublished(
                    to = "<EMAIL>",
                    template = "sign-off-finished",
                    variables = listOf("deleted-by-moderator" to false, "deleted-reason" to DeletedReason.LEAVING_HH),
                    language = "cs",
                ),
            )
            pubSubMock.publish(UserStateChanged(UserStateChange.DELETED, user))
            pubSubMock.publish(
                UserDeleted(
                    userId = "themag",
                    cancelledBy = "themag",
                    cancelledByRole = CancelledByRole.USER,
                    cancelSubscriptions = false,
                    refundSubscriptions = false,
                    deletedReason = DeletedReason.LEAVING_HH,
                    deletedNote = "Going to Forendors",
                ),
            )
        }
    }

    @Test
    fun `user (without an email) should delete his own account - email should still be null`() {
        val testClock = TestClock(Instant.parse("2024-12-13T17:30:30Z"))
        val underTest = DeleteUserCommandService(
            TestCollections.usersCollection,
            TestRepositories.sessionRepository,
            pubSubMock,
            clock = testClock,
            logger = TestLogger,
        )
        val user = testHelper.createUser("themag", email = null, status = UserStatus.ACTIVE, language = "cs")
        val session = testHelper.createSession("themag")

        underTest.execute(
            DeleteUser(
                userId = "themag",
                requesterId = "themag",
                sessionId = session.id,
                cancelSubscriptions = false,
                refundSubscriptions = false,
                deletedReason = DeletedReason.LEAVING_HH,
                deletedNote = "Going to Forendors",
            ),
        )

        val expectedUser = user.copy(
            status = UserStatus.DELETED,
            email = null,
            deletedAt = Instant.parse("2024-12-13T17:30:30Z"),
            deletedReason = DeletedReason.LEAVING_HH,
            deletedNote = "Going to Forendors",
        )

        assertThat(TestCollections.usersCollection[user.id].get()).isEqualTo(expectedUser)
        verify(Ordering.ORDERED) {
            pubSubMock.publish(UserStateChanged(UserStateChange.DELETED, user))
            pubSubMock.publish(
                UserDeleted(
                    userId = "themag",
                    cancelledBy = "themag",
                    cancelledByRole = CancelledByRole.USER,
                    cancelSubscriptions = false,
                    refundSubscriptions = false,
                    deletedReason = DeletedReason.LEAVING_HH,
                    deletedNote = "Going to Forendors",
                ),
            )
        }
    }

    @Test
    fun `admin should delete a creator with 49 subs and refund his subscriptions`() {
        val testClock = TestClock(Instant.parse("2024-12-13T17:30:30Z"))
        val underTest = DeleteUserCommandService(
            TestCollections.usersCollection,
            TestRepositories.sessionRepository,
            pubSubMock,
            clock = testClock,
            logger = TestLogger,
        )
        testHelper.createUser("eliska", role = Role.MODERATOR, permissions = ModeratorPermission.of(DELETE_USERS))
        val user = testHelper.createUser(
            "themag",
            email = null,
            status = UserStatus.ACTIVE,
            language = "cs",
            counts = SupportCounts(supporters = 49),
        )
        val session = testHelper.createSession("eliska", signInProvider = SignInProvider.PASSWORD)

        underTest.execute(
            DeleteUser(
                userId = "themag",
                requesterId = "eliska",
                sessionId = session.id,
                cancelSubscriptions = true,
                refundSubscriptions = true,
                deletedReason = DeletedReason.LEAVING_HH,
                deletedNote = "Going to Forendors",
            ),
        )

        val expectedUser = user.copy(
            status = UserStatus.DELETED,
            email = null,
            deletedAt = Instant.parse("2024-12-13T17:30:30Z"),
            deletedReason = DeletedReason.LEAVING_HH,
            deletedNote = "Going to Forendors",
        )

        assertThat(TestCollections.usersCollection[user.id].get()).isEqualTo(expectedUser)
        verify(Ordering.ORDERED) {
            pubSubMock.publish(UserStateChanged(UserStateChange.DELETED, user))
            pubSubMock.publish(
                UserDeleted(
                    userId = "themag",
                    cancelledBy = "eliska",
                    cancelledByRole = CancelledByRole.MODERATOR,
                    cancelSubscriptions = true,
                    refundSubscriptions = true,
                    deletedReason = DeletedReason.LEAVING_HH,
                    deletedNote = "Going to Forendors",
                ),
            )
        }
    }

    @Nested
    inner class CommandValidation {
        @Test
        fun `user cannot delete another user`() {
            val underTest = DeleteUserCommandService(
                TestCollections.usersCollection,
                TestRepositories.sessionRepository,
                pubSubMock,
                logger = TestLogger,
            )
            testHelper.createUser("themag")
            testHelper.createUser("ordinary-user", role = Role.USER)
            val session = testHelper.createSession("ordinary-user")

            assertThatExceptionOfType(ForbiddenException::class.java)
                .isThrownBy {
                    underTest.execute(
                        DeleteUser(
                            userId = "themag",
                            requesterId = "ordinary-user",
                            sessionId = session.id,
                            cancelSubscriptions = false,
                            refundSubscriptions = false,
                            deletedReason = DeletedReason.LEAVING_HH,
                            deletedNote = "Going to Forendors",
                        ),
                    )
                }
                .withMessage("User ordinary-user is not allowed to delete a different user themag.")

            verify { pubSubMock wasNot called }
        }

        @Test
        fun `moderator cannot delete themselves`() {
            val underTest = DeleteUserCommandService(
                TestCollections.usersCollection,
                TestRepositories.sessionRepository,
                pubSubMock,
                logger = TestLogger,
            )
            testHelper.createUser("eliska", role = Role.MODERATOR)
            val session = testHelper.createSession("eliska")

            assertThatExceptionOfType(ForbiddenException::class.java)
                .isThrownBy {
                    underTest.execute(
                        DeleteUser(
                            userId = "eliska",
                            requesterId = "eliska",
                            sessionId = session.id,
                            cancelSubscriptions = false,
                            refundSubscriptions = false,
                            deletedReason = DeletedReason.LEAVING_HH,
                            deletedNote = "Going to Forendors",
                        ),
                    )
                }
                .withMessage("Moderator eliska cannot delete themselves.")

            verify { pubSubMock wasNot called }
        }

        @Test
        fun `only moderator with delete_permission can delete`() {
            val underTest = DeleteUserCommandService(
                TestCollections.usersCollection,
                TestRepositories.sessionRepository,
                pubSubMock,
                logger = TestLogger,
            )
            testHelper.createUser("themag")
            testHelper.createUser("denis", role = Role.MODERATOR, permissions = ModeratorPermission.of())
            val session = testHelper.createSession("denis")

            assertThatExceptionOfType(ForbiddenException::class.java)
                .isThrownBy {
                    underTest.execute(
                        DeleteUser(
                            userId = "themag",
                            requesterId = "denis",
                            sessionId = session.id,
                            cancelSubscriptions = false,
                            refundSubscriptions = false,
                            deletedReason = DeletedReason.LEAVING_HH,
                            deletedNote = "Going to Forendors",
                        ),
                    )
                }
                .withMessage("Moderator denis is not empowered to delete users.")

            verify { pubSubMock wasNot called }
        }

        @Test
        fun `if user deleted his own account, subscription refunds are not allowed`() {
            val underTest = DeleteUserCommandService(
                TestCollections.usersCollection,
                TestRepositories.sessionRepository,
                pubSubMock,
                logger = TestLogger,
            )
            testHelper.createUser("themag", counts = SupportCounts(supporters = 1))
            val session = testHelper.createSession("themag")

            assertThatExceptionOfType(ForbiddenException::class.java)
                .isThrownBy {
                    underTest.execute(
                        DeleteUser(
                            userId = "themag",
                            requesterId = "themag",
                            sessionId = session.id,
                            cancelSubscriptions = false,
                            refundSubscriptions = true,
                            deletedReason = DeletedReason.LEAVING_HH,
                            deletedNote = "Going to Forendors",
                        ),
                    )
                }
                .withMessage("When user themag deletes themselves, refunds are not allowed.")

            verify { pubSubMock wasNot called }
        }

        @Test
        fun `no one can delete creators with more than 100 subs`() {
            val underTest = DeleteUserCommandService(
                TestCollections.usersCollection,
                TestRepositories.sessionRepository,
                pubSubMock,
                logger = TestLogger,
            )
            testHelper.createUser("themag", counts = SupportCounts(supporters = 101))
            testHelper.createUser(
                "eliskaslaharovaadcsrfqm",
                role = Role.MODERATOR,
                permissions = ModeratorPermission.of(DELETE_USERS),
            )
            val session = testHelper.createSession("eliskaslaharovaadcsrfqm", signInProvider = SignInProvider.PASSWORD)

            assertThatExceptionOfType(ForbiddenException::class.java)
                .isThrownBy {
                    underTest.execute(
                        DeleteUser(
                            userId = "themag",
                            requesterId = "eliskaslaharovaadcsrfqm",
                            sessionId = session.id,
                            cancelSubscriptions = false,
                            refundSubscriptions = false,
                            deletedReason = DeletedReason.LEAVING_HH,
                            deletedNote = "Going to Forendors",
                        ),
                    )
                }
                .withMessage("Not even MODERATOR can delete an account with 101 supporters")

            verify { pubSubMock wasNot called }
        }

        @Test
        fun `moderator cannot delete with revoked session`() {
            val underTest = DeleteUserCommandService(
                TestCollections.usersCollection,
                TestRepositories.sessionRepository,
                pubSubMock,
                logger = TestLogger,
            )
            testHelper.createUser("themag")
            testHelper.createUser(
                "eliskaslaharovaadcsrfqm",
                role = Role.MODERATOR,
                permissions = ModeratorPermission.of(DELETE_USERS),
            )
            val session = testHelper.createSession("eliskaslaharovaadcsrfqm", revoked = true)

            assertThatExceptionOfType(ForbiddenException::class.java)
                .isThrownBy {
                    underTest.execute(
                        DeleteUser(
                            userId = "themag",
                            requesterId = "eliskaslaharovaadcsrfqm",
                            sessionId = session.id,
                            cancelSubscriptions = false,
                            refundSubscriptions = false,
                            deletedReason = DeletedReason.LEAVING_HH,
                            deletedNote = "Going to Forendors",
                        ),
                    )
                }

            verify { pubSubMock wasNot called }
        }

        @Test
        fun `moderator cannot delete with a session that is older than 10 minutes`() {
            val underTest = DeleteUserCommandService(
                TestCollections.usersCollection,
                TestRepositories.sessionRepository,
                pubSubMock,
                logger = TestLogger,
            )
            testHelper.createUser("themag")
            testHelper.createUser(
                "eliskaslaharovaadcsrfqm",
                role = Role.MODERATOR,
                permissions = ModeratorPermission.of(DELETE_USERS),
            )
            val session = testHelper.createSession(
                "eliskaslaharovaadcsrfqm",
                createdAt = Instant.now().minusMinutes(11),
                revoked = false,
                signInProvider = SignInProvider.PASSWORD,
            )

            assertThatExceptionOfType(ForbiddenException::class.java)
                .isThrownBy {
                    underTest.execute(
                        DeleteUser(
                            userId = "themag",
                            requesterId = "eliskaslaharovaadcsrfqm",
                            sessionId = session.id,
                            cancelSubscriptions = false,
                            refundSubscriptions = false,
                            deletedReason = DeletedReason.LEAVING_HH,
                            deletedNote = "Going to Forendors",
                        ),
                    )
                }

            verify { pubSubMock wasNot called }
        }

        @Test
        fun `moderator cannot delete with a session that was not initiated by password`() {
            val underTest = DeleteUserCommandService(
                TestCollections.usersCollection,
                TestRepositories.sessionRepository,
                pubSubMock,
                logger = TestLogger,
            )
            testHelper.createUser("themag")
            testHelper.createUser(
                "eliskaslaharovaadcsrfqm",
                role = Role.MODERATOR,
                permissions = ModeratorPermission.of(DELETE_USERS),
            )
            val session = testHelper.createSession(
                "eliskaslaharovaadcsrfqm",
                createdAt = Instant.now(),
                revoked = false,
                signInProvider = SignInProvider.GOOGLE,
            )

            assertThatExceptionOfType(ForbiddenException::class.java)
                .isThrownBy {
                    underTest.execute(
                        DeleteUser(
                            userId = "themag",
                            requesterId = "eliskaslaharovaadcsrfqm",
                            sessionId = session.id,
                            cancelSubscriptions = false,
                            refundSubscriptions = false,
                            deletedReason = DeletedReason.LEAVING_HH,
                            deletedNote = "Going to Forendors",
                        ),
                    )
                }

            verify { pubSubMock wasNot called }
        }

        @Test
        fun `cancelSubscriptions must be set to true if deleting an account with active subscribers`() {
            val underTest = DeleteUserCommandService(
                TestCollections.usersCollection,
                TestRepositories.sessionRepository,
                pubSubMock,
                logger = TestLogger,
            )
            testHelper.createUser("themag", counts = SupportCounts(supporters = 1))
            val session = testHelper.createSession("themag")

            assertThatExceptionOfType(IllegalStateException::class.java)
                .isThrownBy {
                    underTest.execute(
                        DeleteUser(
                            userId = "themag",
                            requesterId = "themag",
                            sessionId = session.id,
                            cancelSubscriptions = false,
                            refundSubscriptions = false,
                            deletedReason = DeletedReason.LEAVING_HH,
                            deletedNote = "Going to Forendors",
                        ),
                    )
                }
                .withMessage(
                    "Cannot delete account themag with active supporters (1) and cancelSubscriptions set to false.",
                )

            verify { pubSubMock wasNot called }
        }
    }
}
