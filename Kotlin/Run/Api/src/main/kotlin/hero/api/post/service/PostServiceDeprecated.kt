package hero.api.post.service

import hero.api.messages.service.MessageThreadService
import hero.api.post.repository.getPaymentsForPost
import hero.baseutils.hasDuplicates
import hero.baseutils.md5nice
import hero.baseutils.minusDays
import hero.core.logging.Logger
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ConflictException
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.NotFoundException
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.gcloud.imageProxy
import hero.gcloud.increment
import hero.gcloud.root
import hero.gcloud.where
import hero.gjirafa.GjirafaUploadsService
import hero.jackson.map
import hero.model.CategoryDtoRelationship
import hero.model.DocumentAsset
import hero.model.GjirafaStatus.COMPLETE
import hero.model.GjirafaStatus.PARTIALLY_COMPLETED
import hero.model.ImageAsset
import hero.model.ImageAssetDto
import hero.model.MessageThreadDtoV2Relationship
import hero.model.Post
import hero.model.PostAsset
import hero.model.PostAssetDto
import hero.model.PostCounts
import hero.model.PostDto
import hero.model.PostDtoAttributes
import hero.model.PostDtoRelationship
import hero.model.PostDtoRelationships
import hero.model.PostPayment
import hero.model.PostRenderMeta
import hero.model.UserDtoRelationship
import hero.model.topics.PostState
import hero.model.topics.PostStateChange
import hero.model.topics.PostStateChanged
import hero.repository.post.PostRepository
import org.owasp.html.HtmlPolicyBuilder
import org.owasp.html.PolicyFactory
import java.time.Instant
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.util.Optional
import kotlin.jvm.optionals.getOrElse

@Deprecated("Will be replaced by query and command counter parts")
class PostServiceDeprecated(
    private val postsCollection: TypedCollectionReference<Post>,
    private val postRepository: PostRepository,
    private val messageThreadService: MessageThreadService,
    private val gjirafaService: GjirafaUploadsService,
    private val pubSub: PubSub,
    private val postPaymentCollection: TypedCollectionReference<PostPayment>,
    private val log: Logger,
) {
    fun createPost(
        postDto: PostDto,
        userId: String,
        // TODO revert later, see https://linear.app/herohero/issue/HH-2612
        userAgent: String?,
    ): PostDto {
        val now = Instant.now()
        val attributes = postDto.attributes
        val relationships = postDto.relationships
        val postId = generateId(postDto, userId)
        val state = if (postDto.attributes.publishedAt?.isAfter(Instant.now()) == true) {
            PostState.SCHEDULED
        } else {
            PostState.PUBLISHED
        }

        val parentPost = relationships.parent?.id?.let { getPostParent(it) }
        val post = Post(
            id = postId,
            parentId = relationships.parent?.id,
            siblingId = relationships.sibling?.id,
            messageThreadId = relationships.messageThread?.id,
            userId = userId,
            // we duplicate parent (post author) userId so that we can authorize more quickly
            parentUserId = parentPost?.userId ?: userId,
            updated = now,
            // in case of message threads, we must always use current timestamp
            parentPostId = parentPost?.id,
            published = if (relationships.messageThread == null) attributes.publishedAt ?: now else now,
            state = state,
            text = attributes.text?.trim() ?: "",
            textHtml = attributes.textHtml?.let(htmlSanitizer::sanitize)?.trim(),
            textDelta = attributes.textDelta?.trim(),
            excludeFromRss = attributes.excludeFromRss ?: false,
            price = attributes.price,
            categories = relationships.categories.map { it.id },
            chapters = attributes.chapters,
            isAgeRestricted = attributes.isAgeRestricted,
            isSponsored = attributes.isSponsored,
        )

        relationships.messageThread?.id?.run {
            val thread = messageThreadService.get(userId, this, false)
            val canPost = thread.attributes.canPost
            if (canPost == null || !canPost) {
                throw ForbiddenException(
                    "User $userId cannot post to ${thread.id}, attr: ${thread.attributes}",
                    mapOf("userId" to userId),
                )
            }
            post.participingUserIds = thread.relationships.users.map { it.id }
            messageThreadService.reactivateThread(this)
        }

        if (attributes.text.isNullOrBlank() && attributes.assets.isEmpty()) {
            throw BadRequestException(
                "Either text or some assets must be given for a new post by $userId.",
                mapOf(
                    "userId" to userId,
                    "parentId" to relationships.parent?.id,
                    "messageThreadId" to relationships.messageThread?.id,
                ),
            )
        }
        updateAssetsInPost(post, attributes.assets, true, userAgent)
        return save(post, PostStateChange.PUBLISHED).toDto(PostRenderMeta(fullResponse = true))
    }

    fun updatePost(
        postId: String,
        postDto: PostDto,
        userId: String,
        // TODO revert later, see https://linear.app/herohero/issue/HH-2612
        userAgent: String?,
    ): PostDto {
        val post = postsCollection[postId].get()

        post.apply {
            validateBeforeUpdate(this, userId)
            text = text(post, postDto.attributes.text, userId)
            textHtml = textHtml(post, postDto.attributes.textHtml, userId)
            textDelta = postDto.attributes.textDelta
            updated = Instant.now()
            published = publishedAt(post, postDto.attributes.publishedAt, userId)
            if (published.isAfter(Instant.now()) && state == PostState.PUBLISHED) {
                state = PostState.SCHEDULED
            }
            price = price(post, postDto.attributes.price, userId)
            pinnedAt = pinnedAt(post, postDto.attributes.pinnedAt, userId)
            categories = postDto.relationships.categories.map { it.id }
            excludeFromRss = postDto.attributes.excludeFromRss ?: false
            chapters = postDto.attributes.chapters
            isAgeRestricted = postDto.attributes.isAgeRestricted
            isSponsored = postDto.attributes.isSponsored
        }

        updateAssetsInPost(post, postDto.attributes.assets, false, userAgent)
        save(post, PostStateChange.PATCHED)
        log.info("User patched post.", post.map())
        val paymentsFromUserIds = postPaymentCollection.getPaymentsForPost(postId).mapNotNull { it.userId }

        return post.toDto(
            renderMeta = PostRenderMeta(fullResponse = true, paymentsFromUserIds = paymentsFromUserIds),
        )
    }

    fun deletePost(
        postId: String,
        userId: String,
    ) {
        val post = postsCollection[postId].get()
        if (post.userId != userId && post.parentUserId != userId) {
            throw ForbiddenException(
                "You cannot delete other users' posts.",
                mapOf("userId" to userId, "postId" to postId),
            )
        }

        if (post.state == PostState.DELETED) {
            return
        }

        log.info("User deleted post.", mapOf("userId" to userId, "postId" to post.id))
        val now = Instant.now()
        val postRef = postsCollection[postId]
        postRef.field(Post::state).update(PostState.DELETED)
        postRef.field(Post::deletedAt).update(now)

        postRepository.save(post.copy(state = PostState.DELETED, deletedAt = now))

        pubSub.publish(PostStateChanged(PostStateChange.DELETED, post))
    }

    private fun pinnedAt(
        post: Post,
        pinnedAt: Instant?,
        userId: String,
    ): Instant? {
        if (pinnedAt != null && pinnedAt < (post.published ?: Instant.now()).minusDays(DAYS_IN_A_YEAR)) {
            throw BadRequestException(
                "Field pinnedAt ($pinnedAt) cannot be much lower than publishing time.",
                mapOf("userId" to userId, "postId" to post.id),
            )
        }

        return pinnedAt
    }

    private fun publishedAt(
        post: Post,
        publishedAt: Instant?,
        userId: String,
    ) = if (post.messageThreadId == null && post.published != publishedAt) {
        // we should not change publishing of message-thread posts

        val oldPublishAt = post.published
        log.info(
            "Creator $userId is changing publishing of ${post.id} from $oldPublishAt to $publishedAt",
            mapOf("creatorId" to userId, "postId" to post.id),
        )
        publishedAt ?: Instant.now()
    } else {
        post.published
    }

    private fun price(
        post: Post,
        price: Long?,
        userId: String,
    ): Long? {
        val postPrice = if (price == 0L) {
            null
        } else {
            price
        }

        val postPayments = postPaymentCollection.where(PostPayment::postId).isEqualTo(post.id).fetchAll()
        if (post.price != postPrice) {
            if (postPayments.isNotEmpty()) {
                throw ConflictException(
                    "Cannot change price from ${post.price} to $postPrice as there are active payments.",
                    mapOf("creatorId" to userId),
                )
            } else {
                log.info(
                    "Creator $userId is changing price of ${post.id} from ${post.price} to $price",
                    mapOf("creatorId" to userId, "postId" to post.id),
                )
            }
        }
        if (postPrice != null && postPrice < MINIMAL_PRICE) {
            throw BadRequestException(
                "Price must be null or at least 3 €.",
                mapOf("userId" to userId, "postId" to post.id),
            )
        }
        return postPrice
    }

    private fun text(
        post: Post,
        text: String?,
        userId: String,
    ) = text?.trim() ?: throw BadRequestException(
        "Field text must not be null.",
        mapOf("userId" to userId, "postId" to post.id),
    )

    private fun textHtml(
        post: Post,
        textHtml: String?,
        userId: String,
    ) = textHtml
        ?.let { htmlSanitizer.sanitize(it).trim() }
        ?: throw BadRequestException(
            "Field textHtml must not be null.",
            mapOf("userId" to userId, "postId" to post.id),
        )

    private fun validateBeforeUpdate(
        post: Post,
        userId: String,
    ) {
        if (post.userId != userId) {
            throw ForbiddenException(
                "You cannot change other users' posts.",
                mapOf("userId" to userId, "postId" to post.id),
            )
        }
        if (post.state == PostState.DELETED) {
            throw BadRequestException(
                "Cannot patch deleted posts.",
                mapOf("userId" to userId, "postId" to post.id),
            )
        }
    }

    private fun updateAssetsInPost(
        post: Post,
        assetDtos: List<PostAssetDto>,
        isNew: Boolean,
        // TODO revert later, see https://linear.app/herohero/issue/HH-2612
        userAgent: String?,
    ) {
        if (assetDtos.mapNotNull { it.image?.url }.hasDuplicates()) {
            throw ConflictException(
                "Cannot add twice image with the same upload id: ${assetDtos.map { it.image?.url }}",
            )
        }

        val mappedAssets = assetDtos.mapNotNull { mapAsset(it, post.userId, userAgent, post) }
        val youtubeIds = mappedAssets.mapNotNull { it.youTube?.id }
        val gjirafaIds = mappedAssets.mapNotNull { it.gjirafa?.id }
        val gjirafaAssets = mappedAssets.mapNotNull { it.gjirafa }
        val gjirafaLiveIds = mappedAssets.mapNotNull { it.gjirafaLive?.id }

        post.assets = mappedAssets
        post.assetIds = youtubeIds + gjirafaIds + gjirafaLiveIds
        post.assetStates = mappedAssets.mapNotNull { it.gjirafa?.status }.distinct()

        // TODO this cannot be enough not to unpublish published posts when a new media is added
        // we don't want to hide post if already previously published (isNew)
        // if the post is in state SCHEDULED, for example, we want to move it back to processing state
        if (!isNew && post.state == PostState.PUBLISHED) {
            return
        }
        val gjirafaNotReady = gjirafaAssets.any { it.status !in setOf(COMPLETE, PARTIALLY_COMPLETED) }
        if (gjirafaNotReady) {
            post.state = PostState.PROCESSING
        }
        if (mappedAssets.any { it.isEmpty() }) {
            log.fatal("One of assets was empty for post ${post.id}: $mappedAssets", mapOf("userId" to post.userId))
        }
    }

    private fun save(
        post: Post,
        stateChange: PostStateChange,
    ): Post {
        postsCollection[post.id].set(post)
        postRepository.save(post)
        val price = post.price
        if (price != null && price > 0) {
            log.info(
                "Creator ${post.userId} is setting price $price for post ${post.id}",
                mapOf("creatorId" to post.userId, "postId" to post.id),
            )
        }

        if (stateChange == PostStateChange.PUBLISHED) {
            // note that the assets still might be being processed
            post.parentId?.also { parentId -> incrementCounts(parentId) }
        }

        if (post.state == PostState.PUBLISHED) {
            // we can publish the state change ONLY if the post is really processed and published
            pubSub.publish(PostStateChanged(stateChange, post))
        }

        if (post.parentId == null && post.messageThreadId == null) {
            generateRevision(post)
        }

        return post
    }

    private fun generateId(
        postParam: PostDto,
        userId: String,
    ): String {
        val currentTimeMillis = System.currentTimeMillis()
        val messageThreadId = postParam.relationships.messageThread?.id
        val parentId = postParam.relationships.parent?.id
        val postText = postParam.attributes.text
        return if (messageThreadId != null) {
            "$messageThreadId-$currentTimeMillis-${(userId + postParam.attributes.text).md5nice()}"
        } else if (parentId != null) {
            "$parentId-${(userId + currentTimeMillis).md5nice().take(HASH_LENGTH)}"
        } else {
            userId + (postText + userId + currentTimeMillis).md5nice()
        }
    }

    private fun generateRevision(post: Post) {
        val revisionId = "${post.id}-${revisionDateFormat.format(post.updated.atZone(ZoneOffset.UTC))}"
        postsCollection[revisionId].set(
            post.copy(
                id = revisionId,
                state = PostState.REVISION,
            ),
        )
    }

    private fun mapAsset(
        assetDto: PostAssetDto,
        // TODO revert later, see https://linear.app/herohero/issue/HH-2612
        creatorId: String,
        userAgent: String?,
        post: Post,
    ): PostAsset? {
        val image = assetDto.image?.let { image ->
            ImageAsset.of(
                id = image.url ?: throw BadRequestException("Image.url was null."),
                width = image.width ?: throw BadRequestException("Image.width was null."),
                height = image.height ?: throw BadRequestException("Image.height was null."),
            )
        }

        val gjirafa = assetDto.gjirafa?.let {
            try {
                gjirafaService.getAsset(userId = null, assetId = it.id)
            } catch (e: NotFoundException) {
                log.error("Gjirafa asset ${it.id} appears to be deleted, skipping.")
                return@mapAsset null
            }
        }

        val originalGjirafaAssetLive = post.assets
            .firstOrNull {
                it.gjirafa?.id == assetDto.gjirafa?.id
            }
            ?.gjirafaLive

        val gjirafaLive = originalGjirafaAssetLive ?: assetDto.gjirafaLive
        // if this asset was a livestream, hasVideo must be true, otherwise we just use the flag from the asset
        val gjirafaHasVideo = (gjirafaLive != null) || gjirafa?.hasVideo == true
        return PostAsset(
            image = image,
            gjirafa = gjirafa?.copy(hasVideo = gjirafaHasVideo),
            gjirafaLive = gjirafaLive,
            youTube = assetDto.youTube,
            thumbnail = if (assetDto.thumbnail?.startsWith("blob:") == true) {
                log.fatal(
                    "User is trying to store ${assetDto.thumbnail} as a thumbnail, see labels.",
                    mapOf("userAgent" to userAgent, "creatorId" to creatorId),
                )
                null
            } else {
                assetDto.thumbnail
            },
            document = assetDto.document,
        )
    }

    private fun getPost(id: String) = postsCollection[id].get()

    private fun getPostParent(postId: String): Post {
        val post = getPost(postId)
        return post.parentId?.let { getPostParent(it) } ?: post
    }

    private fun incrementCounts(postId: String) {
        postsCollection[postId]
            .field(root(Post::counts).path(PostCounts::comments))
            .increment(1)

        getPost(postId).parentId?.also {
            postsCollection[it]
                .field(root(Post::counts).path(PostCounts::replies))
                .increment(1)
        }
    }
}

fun Post.toDto(renderMeta: PostRenderMeta): PostDto =
    if (state == PostState.DELETED) {
        PostDto(
            id = id,
            attributes = PostDtoAttributes(state),
            relationships = PostDtoRelationships(),
        )
    } else {
        PostDto(
            id = id,
            attributes = PostDtoAttributes(
                publishedAt = published,
                pinnedAt = pinnedAt,
                state = state,
                text = if (renderMeta.showText) text.trim() else null,
                textHtml = if (renderMeta.showText) textHtml?.trim() else null,
                textDelta = if (renderMeta.showText) textDelta?.trim() else null,
                fullAsset = renderMeta.fullResponse,
                counts = counts,
                excludeFromRss = excludeFromRss ?: false,
                assets = when {
                    assets.isNotEmpty() -> assets.filter { !it.isEmpty() }.map { it.toDto(renderMeta) }
                    // https://gitlab.com/heroheroco/general/-/issues/732
                    // handling legacy single-asset posts
                    else -> listOf()
                },
                price = price,
                assetsCount = when {
                    assets.any { !it.isEmpty() } -> assets.size
                    else -> 0
                },
                chapters = chapters ?: emptyList(),
                isAgeRestricted = isAgeRestricted,
                isSponsored = isSponsored,
            ),
            relationships = PostDtoRelationships(
                parent = parentId?.let { PostDtoRelationship(it) },
                sibling = siblingId?.let { PostDtoRelationship(it) },
                user = UserDtoRelationship(userId),
                messageThread = messageThreadId?.let { MessageThreadDtoV2Relationship(it) },
                paymentsByUsers = renderMeta.paymentsFromUserIds?.map { UserDtoRelationship(it) } ?: emptyList(),
                // TODO some posts are without categories right now will be fixed in https://gitlab.com/heroheroco/general/-/issues/1578
                categories = Optional.ofNullable(categories).getOrElse { emptyList() }
                    .map { CategoryDtoRelationship(it) },
            ),
        )
    }

fun PostAsset.toDto(renderMeta: PostRenderMeta): PostAssetDto =
    PostAssetDto(
        image = image?.let { image ->
            ImageAssetDto(
                url = if (renderMeta.fullResponse) image.id.imageProxy() else null,
                width = image.width,
                height = image.height,
            )
        },
        youTube = if (renderMeta.fullResponse) youTube else null,
        gjirafa = if (renderMeta.fullResponse) {
            if (gjirafaLive != null) {
                gjirafa?.copy(hasVideo = true)
            } else {
                gjirafa
            }
        } else {
            null
        },
        gjirafaLive = if (renderMeta.fullResponse) gjirafaLive else null,
        document = document?.takeIf { renderMeta.fullResponse }?.let { DocumentAsset(it.url, it.type, it.name) },
        thumbnail = thumbnail?.takeIf { renderMeta.fullResponse },
    )

internal val htmlCleaner: PolicyFactory = HtmlPolicyBuilder()
    .toFactory()

internal val htmlSanitizer: PolicyFactory = HtmlPolicyBuilder()
    .allowElements("a", "p", "span", "strong", "i", "em")
    .allowStandardUrlProtocols()
    .allowAttributes("class", "data-seconds").onElements("span")
    .allowAttributes("href", "rel", "target", "title").onElements("a")
    .requireRelNofollowOnLinks()
    .toFactory()

private val revisionDateFormat: DateTimeFormatter =
    DateTimeFormatter.ofPattern("yyyyMMddHHmmss")

private const val HASH_LENGTH = 8
private const val DAYS_IN_A_YEAR = 365L
private const val MINIMAL_PRICE = 3
