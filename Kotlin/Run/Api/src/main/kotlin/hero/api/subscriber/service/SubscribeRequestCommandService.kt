package hero.api.subscriber.service

import hero.api.subscriber.repository.SubscriberStripeRepository
import hero.exceptions.http.ConflictException
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.NotFoundException
import hero.model.CZ_VAT_COUNTRY
import hero.model.Currency
import hero.model.FREE_SUBSCRIBER_TIER_ID
import hero.model.Notification
import hero.model.NotificationType
import hero.model.StorageEntityType
import hero.model.SubscribeRequest
import hero.model.Tier
import hero.repository.notification.NotificationRepository
import hero.repository.subscription.JooqSubscriptionHelper
import hero.repository.subscription.SubscribeRequestRepository
import hero.repository.user.UserRepository
import hero.sql.jooq.Tables
import hero.sql.jooq.tables.Subscription.SUBSCRIPTION
import hero.stripe.service.StripeSubscriptionService
import org.jooq.DSLContext
import java.time.Clock
import java.time.Instant

class SubscribeRequestCommandService(
    private val subscribeRequestRepository: SubscribeRequestRepository,
    private val userRepository: UserRepository,
    private val subscriberStripeRepository: SubscriberStripeRepository,
    private val stripeSubscriptionService: StripeSubscriptionService,
    private val notificationRepository: NotificationRepository,
    lazyContext: Lazy<DSLContext>,
    private val clock: Clock = Clock.systemUTC(),
) {
    private val context by lazyContext

    fun execute(command: RequestToSubscribe): SubscribeRequest {
        val previousRequest = subscribeRequestRepository.findSingle {
            this
                .where(Tables.SUBSCRIBE_REQUEST.USER_ID.eq(command.userId))
                .and(Tables.SUBSCRIBE_REQUEST.CREATOR_ID.eq(command.creatorId))
                .and(Tables.SUBSCRIBE_REQUEST.ACCEPTED_AT.isNull)
                .and(Tables.SUBSCRIBE_REQUEST.DECLINED_AT.isNull)
                .and(Tables.SUBSCRIBE_REQUEST.DELETED_AT.isNull)
        }

        if (previousRequest != null) {
            throw ConflictException(
                "User ${command.userId} has already requested to subscribe to creator ${command.creatorId}",
            )
        }

        val subscription = context.selectFrom(SUBSCRIPTION)
            .where(SUBSCRIPTION.USER_ID.eq(command.userId).and(SUBSCRIPTION.CREATOR_ID.eq(command.creatorId)))
            .and(JooqSubscriptionHelper.activeSubscription)
            .fetch()
        if (subscription.isNotEmpty) {
            throw ConflictException("User ${command.userId} already subscribes to creator ${command.creatorId}")
        }

        val request = SubscribeRequest(
            userId = command.userId,
            creatorId = command.creatorId,
            createdAt = Instant.now(clock),
            acceptedAt = null,
            declinedAt = null,
            deletedAt = null,
        )

        return subscribeRequestRepository.save(request)
    }

    fun execute(command: AcceptSubscribeRequest) {
        val request = subscribeRequestRepository.getById(command.requestId)

        if (request.creatorId != command.userId) {
            throw ForbiddenException()
        }

        val subscription = context.selectFrom(SUBSCRIPTION)
            .where(SUBSCRIPTION.USER_ID.eq(command.userId).and(SUBSCRIPTION.CREATOR_ID.eq(request.creatorId)))
            .and(JooqSubscriptionHelper.activeSubscription)
            .fetch()
        if (subscription.isNotEmpty) {
            throw ConflictException("User ${command.userId} already subscribes to creator ${request.creatorId}")
        }

        if (request.deletedAt != null || request.acceptedAt != null || request.declinedAt != null) {
            throw NotFoundException()
        }

        val creator = userRepository.getById(request.creatorId)
        val price = subscriberStripeRepository.priceFactory(creator, FREE_SUBSCRIBE_TIER)
        val customerId = subscriberStripeRepository.customerFactory(request.userId, FREE_SUBSCRIBE_TIER.currency)

        stripeSubscriptionService.createSubscription(
            customerId = customerId,
            paymentMethodId = null,
            couponId = null,
            tier = FREE_SUBSCRIBE_TIER,
            priceId = price.id,
            creatorId = request.creatorId,
            userId = request.userId,
            creatorStripeAccountId = null,
            onBehalfOf = null,
            creatorCountry = CZ_VAT_COUNTRY,
            creatorVatId = null,
            subscribed = Instant.now(),
            isResubscription = false,
            cardCreateType = null,
            currency = Currency.EUR,
        )

        context
            .update(Tables.SUBSCRIBE_REQUEST)
            .set(Tables.SUBSCRIBE_REQUEST.ACCEPTED_AT, Instant.now(clock))
            .where(Tables.SUBSCRIBE_REQUEST.ID.eq(command.requestId))
            .execute()

        val notification = Notification(
            userId = request.userId,
            type = NotificationType.SUBSCRIBE_REQUEST_ACCEPTED,
            actorIds = mutableListOf(request.creatorId),
            objectType = StorageEntityType.USER,
            objectId = request.creatorId,
            created = Instant.now(clock),
            timestamp = Instant.now(clock),
        )
        notificationRepository.save(notification)
    }
}

data class RequestToSubscribe(val creatorId: String, val userId: String)

data class AcceptSubscribeRequest(val userId: String, val requestId: Long)

private val FREE_SUBSCRIBE_TIER = Tier(FREE_SUBSCRIBER_TIER_ID, 0, false, Currency.EUR, false)
