package hero.api.payment.service

import com.stripe.model.Coupon
import com.stripe.param.CouponCreateParams
import hero.api.payment.controller.PaymentResponse
import hero.api.subscriber.repository.SubscriberStripeRepository
import hero.api.user.repository.UsersRepository
import hero.baseutils.log
import hero.exceptions.http.ConflictException
import hero.model.CouponMethod
import hero.model.Subscriber
import hero.model.Tier
import hero.model.User
import hero.model.topics.CardCreateType
import hero.stripe.service.StripeClients
import java.util.UUID

class StripeAppleMirroringService(
    private val subscriberStripeRepository: SubscriberStripeRepository,
    private val clients: StripeClients,
    private val userRepository: UsersRepository,
) {
    private fun appleCouponFactory(
        userId: String,
        creator: User,
        tier: Tier,
    ): Coupon {
        val price = subscriberStripeRepository.priceFactory(creator, tier)
        return clients[tier.currency].coupons().create(
            CouponCreateParams.builder()
                .setId("APPLE-${tier.currency}-${UUID.randomUUID()}")
                .setDuration(CouponCreateParams.Duration.FOREVER)
                .setAppliesTo(
                    CouponCreateParams.AppliesTo.builder()
                        .addProduct(price.product)
                        .build(),
                )
                .setPercentOff(100.toBigDecimal())
                .setMetadata(
                    mapOf(
                        "purchasedByUserId" to userId,
                        "couponMethod" to CouponMethod.APPLE_IN_APP.name,
                        "creatorId" to creator.id,
                    ),
                )
                .build(),
        )
    }

    fun createSubscription(
        appleReferenceId: String,
        appleTransactionId: String,
        userId: String,
        creatorId: String,
        tierId: String,
    ): PaymentResponse {
        val tier = Tier.ofId(tierId)
        val user = userRepository.get(userId)
        val creator = userRepository.get(creatorId)

        if (!creator.creator.active) {
            throw ConflictException(
                "Creator ${creator.id} has not finished their Stripe account pairing.",
                mapOf("creatorId" to creator.id, "appleReferenceId" to appleReferenceId),
            )
        }

        if (creator.id == user.id) {
            throw ConflictException(
                "User ${user.id} cannot subscribe themselves.",
                mapOf("userId" to user.id, "appleReferenceId" to appleReferenceId),
            )
        }

        val coupon = appleCouponFactory(userId, creator, tier)
        val subscription = subscriberStripeRepository.subscribe(
            user = user,
            creator = creator,
            paymentMethodId = null,
            couponId = coupon.id,
            cardCreateType = CardCreateType.APPLE_IN_APP,
            metadata = mapOf(
                Subscriber::appleReferenceId.name to appleReferenceId,
                Subscriber::appleTransactionId.name to appleTransactionId,
            ),
        )

        log.info(
            "Replicated Apple subscription $appleReferenceId of ${user.id} -> ${creator.id}" +
                " as ${subscription.attributes.stripeId}/${subscription.attributes.subscriptionStatus}",
            mapOf("userId" to user.id, "creatorId" to creator.id, "appleReferenceId" to appleReferenceId),
        )
        return subscription
    }

    fun appleCancelledAtPeriodEnd(
        transactionId: String,
        cancelAtPeriodEnd: Boolean,
    ) {
        subscriberStripeRepository.patchSubscription(transactionId, cancelAtPeriodEnd)
    }

    fun appleCancelledNow(appleReferenceId: String) {
        subscriberStripeRepository.cancelSubscription(appleReferenceId)
    }
}
