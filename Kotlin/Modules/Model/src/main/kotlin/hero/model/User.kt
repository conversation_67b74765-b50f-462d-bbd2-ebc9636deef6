package hero.model

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.google.cloud.firestore.annotation.Exclude
import hero.baseutils.nullIfEmpty
import hero.core.annotation.NoArg
import hero.core.data.EntityCollection
import java.time.Instant
import javax.validation.constraints.Size

@NoArg
data class User(
    var id: String,
    var name: String,
    var email: String? = null,
    var emailVerified: Boolean = false,
    var bio: String = "",
    var bioHtml: String = "",
    var bioEn: String = "",
    var bioHtmlEn: String = "",
    var path: String,
    var pathChanged: Instant = Instant.ofEpochSecond(0),
    var role: Role = Role.USER,
    var affiliateSource: String? = null,
    var language: String = "en",
    var status: UserStatus = UserStatus.ACTIVE,
    var facebookId: String? = null,
    var googleId: String? = null,
    var firebaseId: String? = null,
    var airTableId: String? = null,
    var hasDrm: Boolean? = false,
    var hasRssFeed: Boolean = false,
    /** see spec: https://linear.app/herohero/issue/HH-3635/oss-for-creators */
    var oneStopShopAt: Instant? = null,
    var privacyPolicyEffectiveAt: Instant? = null,
    var explicit: Boolean = false,
    val featured: Boolean? = false,
    /** distinguishes languages for which the creators is considered `featured` */
    val featuredBy: List<String> = emptyList(),
    var image: ImageAsset? = null,
    var counts: SupportCounts = SupportCounts(),
    var creator: Creator,
    var customerIds: CustomerIds = mutableMapOf(),
    var notificationsEnabled: NotificationsEnabled = NotificationsEnabled(),
    var company: UserCompany? = null,
    var discord: DiscordMeta? = null,
    var analytics: Analytics = Analytics(),
    var created: Instant = Instant.now(),
    var lastPostAt: Instant? = null,
    var deletedAt: Instant? = null,
    val deletedReason: DeletedReason? = null,
    val deletedNote: String? = null,
    val lastChargeFailedAt: Instant? = null,
    var hasLivestreams: Boolean = false,
    var gjirafaLivestream: GjirafaLivestreamMeta? = null,
    /** true if user allowed to export its posts to spotify (independent on `spotify` field below) */
    var hasSpotifyExport: Boolean = false,
    /** Spotify metadata needed to update permissions of user in Spotify BE */
    var spotify: SpotifyMeta? = null,
    /** URI of the related Spotify show */
    var spotifyUri: String? = null,
    var spotifyFeedReady: Boolean? = false,
    var isOfAge: Boolean = false,
    val moderatorPermissions: Int? = if (role == Role.MODERATOR) 0 else null,
    // If true, subscribers are restricted to using only one coupon(ever) for this creator.
    val subscribersSingleCoupon: Boolean = false,
    val verifiedAt: Instant? = null,
) {
    @get:Exclude
    val isStripeCustomer: Boolean
        get() = customerIds.isNotEmpty()

    @get:Exclude
    val isEmailVerifiedOrThirdParty: Boolean
        get() = emailVerified || facebookId != null || googleId != null

    // https://linear.app/herohero/issue/HH-3372/impossible-to-gift-subscription#comment-87685c75
    @get:Exclude
    val hasGiftsAllowed: Boolean
        get() =
            (
                counts.supporters >= 10 &&
                    (counts.incomes ?: 0) >= 50 &&
                    (counts.invoices ?: 0) >= 1
            ).or(
                counts.supporters >= 50 &&
                    (counts.incomes ?: 0) >= 150,
            ).or(
                role == Role.MODERATOR,
            )

    @get:Exclude
    val companyOrPhysicalPerson: UserCompany
        get() = (company ?: UserCompany()).let {
            it.copy(
                namePublic = name,
                name = it.name,
                // TODO https://linear.app/herohero/issue/HH-840/distinguish-company-from-individual
                isIndividual = !(it.name ?: name).contains("s[.\\s]*r[.\\s]*o") &&
                    !(it.name ?: name).contains("a[.\\s]*s[.\\s]*"),
                country = it.country?.nullIfEmpty()?.uppercase() ?: CZ_VAT_COUNTRY,
            )
        }

    fun customerId(creator: Creator): String? {
        val tier = Tier.ofId(creator.tierId)

        return customerIds[tier.currency.name]
    }

    companion object : EntityCollection<User> {
        override val collectionName: String = "users"
    }
}

enum class DeletedReason {
    LEAVING_HH,
    DUPLICATE_ACCOUNT,
    VIOLATING_TERMS,
    VIOLATING_COMMUNITY_GUIDELINES,
    FAKE_ACCOUNT,
    OTHER,
}

@NoArg
data class UserCompany(
    /** public facing name, eg. "Sugar Denny" */
    var namePublic: String? = null,
    @Deprecated("Use `companyType` as described in HH-1433")
    var isIndividual: Boolean? = false,
    var companyType: CompanyType? = null,
    /** official company name, eg. "Vaultic s.r.o." */
    var name: String? = null,
    var firstName: String? = null,
    var lastName: String? = null,
    var address: String? = null,
    var postalCode: String? = null,
    var country: String? = null,
    var birthDate: String? = null,
    var city: String? = null,
    var state: String? = null,
    var phone: String? = null,
    /** company id in europe, employer id (EIN) in US */
    var id: String? = null,
    /** EU official vatId starting with country suffix, eg. DE123456789*/
    var vatId: String? = null,
    /** text which should appear on the invoice next to the company spec */
    var additionalInfo: String? = null,
    var registeredWith: String? = null,
    var vatType: VatPayer = VatPayer.NON_VAT_PAYER,
    var iban: String? = null,
    var swift: String? = null,
)

@NoArg
data class UserCompanyPublic(
    /** public facing name, eg. "Sugar Denny" */
    var namePublic: String? = null,
    var companyType: CompanyType = CompanyType.INDIVIDUAL,
    /** official company name, eg. "Vaultic s.r.o." */
    var name: String? = null,
    var firstName: String? = null,
    var lastName: String? = null,
    var address: String? = null,
    var postalCode: String? = null,
    var country: String? = null,
    var city: String? = null,
    var state: String? = null,
    /** company id in europe, employer id (EIN) in US */
    var id: String? = null,
    /** EU official vatId starting with country suffix, eg. DE123456789*/
    var vatId: String? = null,
    /** text which should appear on the invoice next to the company spec */
    var additionalInfo: String? = null,
    var registeredWith: String? = null,
    var vatType: VatPayer = VatPayer.NON_VAT_PAYER,
    var vatRate: Int? = null,
)

fun UserCompany.toPublic(): UserCompanyPublic =
    UserCompanyPublic(
        namePublic = namePublic,
        companyType = companyType ?: CompanyType.INDIVIDUAL,
        name = name,
        firstName = firstName,
        lastName = lastName,
        address = address,
        postalCode = postalCode,
        country = country,
        city = city,
        state = state,
        id = id,
        vatId = vatId,
        additionalInfo = additionalInfo,
        registeredWith = registeredWith,
        vatType = vatType,
    )

enum class CompanyType {
    NO_COMPANY,
    INDIVIDUAL,
    LEGAL_ENTITY,
}

enum class VatPayer {
    NON_VAT_PAYER,
    VAT_PAYER,
    EXEMPTED_FROM_VAT,
}

@NoArg
data class SpotifyMeta(
    var id: String,
    var accessToken: String,
    var refreshToken: String,
    var tokenExpiresAt: Instant,
)

data class DiscordMeta(
    var id: String? = null,
    var guildId: String? = null,
    var roleId: String? = null,
    var accessToken: String? = null,
    var refreshToken: String? = null,
    var tokenExpiresAt: Instant? = null,
    var active: Boolean = false,
)

enum class UserStatus {
    @JsonProperty("active")
    ACTIVE,

    @JsonProperty("deleted")
    DELETED,
}

data class SupportCounts(
    var supporters: Long = 0,
    var supporting: Long = 0,
    val supportersThreshold: Long? = null,
    // nullables for output response
    var incomes: Long? = 0,
    var incomesClean: Long? = 0,
    var payments: Long? = 0,
    var posts: Long? = 0,
    var invoices: Long? = 0,
)

@NoArg
data class NotificationsEnabled(
    var emailNewPost: Boolean = true,
    var emailNewDm: Boolean = true,
    val pushNewPost: Boolean = true,
    val pushNewComment: Boolean = true,
    var newsletter: Boolean = true,
    var termsChanged: Boolean = true,
)

enum class Role {
    USER,
    MODERATOR,
}

data class UserDtoListResponse(
    val meta: ListResponseMeta,
    val users: List<UserDto>,
    val included: UserDtoIncluded,
)

data class UserDtoIncluded(
    var tiers: List<TierDto>,
    var categories: List<CategoryDto>,
)

data class UserIdResponse(
    val userId: String,
    val role: Role,
    val state: UserStateChange,
    val emailVerified: Boolean,
)

typealias CustomerIds = MutableMap<String, String>

data class UserDetails(
    var email: String?,
    var creator: Creator? = null,
    var discord: DiscordDtoAttributes? = null,
    var notificationsEnabled: NotificationsEnabled? = null,
    var language: String = "cs",
)

@NoArg
data class Creator(
    var tierId: String,
    var stripeAccountId: String? = null,
    val stripeAccountCreatedAt: Instant? = null,
    val stripeAccountSuggestionSentAt: Instant? = null,
    val stripeAccountIdInEu: String? = null,
    val stripeAccountIdInUs: String? = null,
    val stripeAccountTestInUs: String? = null,
    /**
     * When primary stripe account is removed, this list is to keep the previous ones
     * for handling previously active subscriptions and payments.
     */
    var stripeAccountLegacyIds: List<String> = listOf(),
    /**
     * True if the creator was approved by Stripe and start accepting subscribers/payments.
     * Currently this field is inferred as `requirements.disabledReason == null && !requirements.deleted`
     * See StripeController.routeStripeReturn for details.
     */
    var stripeAccountActive: Boolean = false,
    /**
     * True if the creator finished the first onboarding process. This does not mean they can accept payments,
     * this only means that user should be redirected to their Stripe account detail page instead of Stripe
     * account creation page, when clicking "My Stripe profile".
     */
    var stripeAccountOnboarded: Boolean = false,
    /**
     * List of Stripe requirements for the profiles to be active. Some of them might be required
     * in future, but not now. We use this mostly for debugging as `stripeAccountActive` and inferring
     * stripeAccountActive field above.
     */
    var stripeRequirements: StripeRequirements? = null,
    var suspended: Boolean = false,
    var currency: Currency? = null,
    val emailPublic: String? = null,
    val emailInvoice: String? = null,
) {
    @get:Exclude
    val active: Boolean
        get() = stripeAccountId != null && stripeAccountActive && !suspended

    @get:Exclude
    val verified: Boolean
        get() = active && (stripeRequirements?.let { it.eventuallyDue.isEmpty() && it.currentlyDue.isEmpty() } ?: false)
}

data class UserDto(
    override var id: String,
    override val attributes: UserDtoAttributes,
    override val relationships: UserDtoRelationships,
) : JsonApiEntity {
    override val type: String = "user"
}

data class UserDtoAttributes(
    @get:Size(min = 2, max = 64)
    var name: String? = null,
    @get:Size(min = 0, max = 1500)
    var bio: String? = null,
    @get:Size(min = 0, max = 1500)
    var bioHtml: String? = null,
    @get:Size(min = 0, max = 1500)
    var bioEn: String? = null,
    @get:Size(min = 0, max = 1500)
    var bioHtmlEn: String? = null,
    var createdAt: Instant? = null,
    var image: ImageAsset? = null,
    var path: String? = null,
    var pathChangeableAt: Instant? = null,
    var subscribable: Boolean = false,
    var verified: Boolean = false,
    var stripeAccountId: String? = null,
    var creatorSuspended: Boolean? = false,
    var counts: SupportCounts = SupportCounts(),
    var status: UserStatus = UserStatus.ACTIVE,
    var hasRssFeed: Boolean = false,
    var hasDrm: Boolean = false,
    var hasSpotifyExport: Boolean = false,
    var hasSpotifyConnection: Boolean = false,
    var hasLivestreams: Boolean = false,
    var hasGiftsAllowed: Boolean = false,
    var spotifyUri: String? = null,
    var spotifyFeedReady: Boolean = false,
    var language: String? = null,
    var discord: DiscordDtoAttributes? = null,
    var notificationsEnabled: NotificationsEnabled? = null,
    var analytics: Analytics = Analytics(),
    var company: UserCompany? = null,
    var privacyPolicyEffectiveAt: Instant? = null,
    var email: String? = null,
    var emailVerified: Boolean? = null,
    var lastChargeFailedAt: Instant?,
    var gjirafaLivestream: GjirafaLivestreamMeta?,
    var isOfAge: Boolean? = null,
)

data class UserDtoRelationships(
    // cannot be non-nullable (for now) because of
    // `if (user.status == UserStatus.DELETED)` block in `UsersRepository`
    var tier: TierDtoRelationship?,
    var categories: List<CategoryDtoRelationship>?,
)

@NoArg
// we don't want to publicly show which tracking we support in general
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Analytics(
    val facebookPixelId: String? = null,
    val ga4Stream: String? = null,
    val googleAdsConversionId: String? = null,
    val googleAdsConversionLabel: String? = null,
    val tiktokPixelId: String? = null,
    val leadHub: String? = null,
)

data class DiscordDtoAttributes(
    val id: String,
    val guildId: String?,
)

data class TierDtoRelationship(
    val id: String,
) {
    val type: String = "tier"
}

data class UserStateChanged(
    val stateChange: UserStateChange,
    val user: User,
)

enum class UserStateChange {
    CREATED,
    PATCHED,
    DELETED,
}

data class ExtractedUser(
    // provider identification of current user
    var id: String,
    // secondary identification of provider (eg. discord guildId)
    var secondaryId: String?,
    // used only when connecting OAuth provider to already existing user
    var userId: String?,
    var provider: OAuthProvider,
    var name: String,
    var email: String?,
    var imageUrl: String?,
    var affiliateSource: String? = null,
    var accessToken: String?,
    var refreshToken: String?,
    var tokenExpiresAt: Instant?,
    var meta: Map<String, Any?> = mapOf(),
    var language: String? = null,
)

enum class OAuthProvider {
    DISCORD,
    FIREBASE,
    SPOTIFY,
}

enum class SubscriptionsDtoStatus {
    @JsonProperty("active")
    ACTIVE,

    @JsonProperty("past_due")
    PAST_DUE,

    @JsonProperty("inactive")
    INACTIVE,
}

data class SubscriptionsDtoIncluded(
    val users: List<UserDto> = emptyList(),
    val tiers: List<TierDto> = emptyList(),
    val categories: List<CategoryDto> = emptyList(),
)

data class SubscriptionDto(
    val id: String,
    val attributes: SubscriptionDtoAttributes,
    val relationships: SubscriptionDtoRelationships,
) {
    val type: String = "subscription"
}

data class SubscriptionDtoAttributes(
    val subscribedAt: Instant?,
    val status: SubscriptionsDtoStatus?,
    val cancelAtPeriodEnd: Boolean?,
    val expires: Instant?,
    val type: SubscriberType?,
    val couponAppliedForMonths: Long?,
    val couponAppliedForDays: Long?,
    val couponMethod: CouponMethod?,
    val couponPercentOff: Long?,
    val couponExpiresAt: Instant?,
)

data class SubscriptionDtoRelationships(
    val user: UserDtoRelationship,
    val creator: UserDtoRelationship,
    val tier: TierDtoRelationship?,
)

data class SubscriptionsDtoResponse(
    val meta: ListResponseMeta,
    val subscriptions: List<SubscriptionDto>,
    val included: SubscriptionsDtoIncluded,
)

@NoArg
data class Path(
    val id: String,
    val userId: String,
    val created: Instant = Instant.now(),
    val abandoned: Instant? = null,
) {
    companion object : EntityCollection<Path> {
        override val collectionName: String = "paths"
    }
}

@NoArg
data class GjirafaLivestreamMeta(
    val publicId: String,
    val streamUrl: String,
    val streamKey: String,
    /**
     * Field will only be available after starting the stream to the streamUrl.
     * We will be notified of the generated playbackUrl via webhooks.
     */
    val playbackUrl: String?,
)

data class UserDtoRelationship(
    val id: String,
) {
    val type: String = "user"
}

data class RssFeed(
    val iat: Long? = null,
    val clients: List<String>? = listOf(),
) {
    companion object : EntityCollection<RssFeed> {
        override val collectionName: String = "rss-feeds"
    }
}

data class StripeRequirements(
    val stripeAccountId: String? = null,
    val deleted: Boolean = false,
    // https://stripe.com/docs/api/accounts/object#account_object-requirements-disabled_reason
    val disabledReason: String? = null,
    val currentlyDue: List<String> = listOf(),
    val eventuallyDue: List<String> = listOf(),
    val pastDue: List<String> = listOf(),
    val errors: List<String> = listOf(),
    val pendingVerification: List<String> = listOf(),
) {
    val valid: Boolean =
        // it.pendingVerification.isEmpty() is not necessary to start accepting payments
        // eg.: [individual.verification.additional_document, individual.verification.document]
        disabledReason == null && !deleted
}
