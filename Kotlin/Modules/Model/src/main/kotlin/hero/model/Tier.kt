package hero.model

import com.fasterxml.jackson.annotation.JsonProperty
import hero.core.annotation.NoArg
import hero.core.data.EntityCollection
import java.math.BigDecimal
import java.math.RoundingMode

@NoArg
data class Tier(
    var id: String,
    @Deprecated("Use more precise `priceCents`.")
    var price: Int,
    var default: Boolean,
    var currency: Currency,
    var hidden: Boolean,
    var feeDecimal: BigDecimal? = null,
) {
    // for the sake of simplicity (and testing), we want to avoid comparing all fields
    override fun equals(other: Any?): Boolean =
        if (other is Tier) {
            id == other.id
        } else {
            super.equals(other)
        }

    override fun hashCode(): Int = id.hashCode()

    val priceCents: Int
        get() = price

    val priceDecimal: BigDecimal
        get() = priceCents.toBigDecimal().setScale(2, RoundingMode.HALF_UP).movePointLeft(2)

    /** Herohero fee in percents */
    val heroheroFee: BigDecimal = when (currency) {
        Currency.EUR -> BigDecimal("10.00")
        Currency.USD -> BigDecimal("7.00")
        else -> error("Cannot compute fee for $currency")
    }

    /** Dynamic stripe fee in percents */
    val stripeFeeDynamic = when (currency) {
        Currency.EUR -> BigDecimal("0.00")
        Currency.USD -> BigDecimal("2.90")
        else -> error("Cannot compute fee for $currency")
    }

    /** Stripe fixed fee in given currency. */
    val stripeFeeFixed = when (currency) {
        Currency.EUR -> BigDecimal("0.00")
        Currency.USD -> BigDecimal("0.30")
        else -> error("Cannot compute fee for $currency")
    }

    /** Computed absolute sum of all fees in given currency (without potential VAT). */
    val feeAbsolute: BigDecimal
        get() = (priceDecimal * heroheroFee.movePointLeft(2))
            .plus(priceDecimal * stripeFeeDynamic.movePointLeft(2))
            .plus(stripeFeeFixed)
            .setScale(2, RoundingMode.HALF_UP)

    /** Computed relative fee in percents. */
    val feePercents: BigDecimal
        get() = feeAbsolute.divide(priceDecimal, 4, RoundingMode.HALF_UP)
            .movePointRight(2)

    val dynamicStripeFeeAbsolute = stripeFeeDynamic.setScale(4)
        .times(priceDecimal)
        .movePointLeft(2)
        .setScale(2, RoundingMode.HALF_UP)

    val heroheroFeeAbsolute = feeAbsolute.minus(dynamicStripeFeeAbsolute)
        .minus(stripeFeeFixed)
        .setScale(2, RoundingMode.HALF_UP)

    companion object : EntityCollection<Tier> {
        override val collectionName: String = "tiers"
        override val hasEnvironmentPrefix: Boolean = false

        /** Allows to construct the Tier object based on its id. */
        fun ofId(tierId: String): Tier =
            Tier(
                id = tierId,
                currency = Currency.valueOf(tierId.substring(0, CURRENCY_LENGTH)),
                price = tierId.substring(CURRENCY_LENGTH).toInt().times(100),
                default = false,
                hidden = false,
            )

        private const val CURRENCY_LENGTH = 3
    }
}

data class TierDto(
    override var id: String? = null,
    override val attributes: TierDtoAttributes,
    override val relationships: TierDtoRelationships,
) : JsonApiEntity {
    override val type: String = "tier"
}

data class TierDtoAttributes(
    var price: Int? = null,
    var default: Boolean = false,
    var hidden: Boolean = false,
    var currency: Currency,
)

object TierDtoRelationships

enum class Currency(val symbol: String) {
    @JsonProperty("eur")
    EUR("€"),

    @JsonProperty("usd")
    USD("$"),

    @JsonProperty("czk")
    CZK("CZK"),

    @JsonProperty("gbp")
    GBP("£"),

    @JsonProperty("pln")
    PLN("PLN"),

    @JsonProperty("aud")
    AUD("AU\$"),

    @JsonProperty("chf")
    CHF("CHF"),
}

const val FREE_SUBSCRIBER_TIER_ID = "EUR00"
