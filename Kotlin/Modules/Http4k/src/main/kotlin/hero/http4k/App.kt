package hero.http4k

import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.systemEnv
import hero.baseutils.systemEnvRelaxed
import hero.core.sentry.initializeSentry
import hero.exceptions.http.ForbiddenException
import hero.http4k.auth.parseJwtUser
import hero.http4k.config.cors
import hero.http4k.config.notFoundThrower
import hero.http4k.config.onRequest
import hero.http4k.controller.static
import hero.http4k.controller.swaggerUi
import hero.http4k.extensions.CustomJacksonWithoutNulls
import hero.http4k.extensions.userAgent
import io.opentelemetry.api.OpenTelemetry
import io.opentelemetry.api.common.AttributeKey
import io.opentelemetry.api.common.Attributes
import io.opentelemetry.api.trace.SpanBuilder
import io.opentelemetry.context.propagation.ContextPropagators
import io.opentelemetry.exporter.otlp.trace.OtlpGrpcSpanExporter
import io.opentelemetry.extension.trace.propagation.B3Propagator
import io.opentelemetry.sdk.OpenTelemetrySdk
import io.opentelemetry.sdk.trace.SdkTracerProvider
import io.opentelemetry.sdk.trace.export.BatchSpanProcessor
import org.http4k.contract.ContractRoute
import org.http4k.contract.contract
import org.http4k.contract.jsonschema.v3.NoFieldFound
import org.http4k.contract.openapi.ApiInfo
import org.http4k.contract.openapi.v3.OpenApi3
import org.http4k.contract.security.Security
import org.http4k.core.Filter
import org.http4k.core.Method
import org.http4k.core.Request
import org.http4k.core.then
import org.http4k.filter.OpenTelemetryTracing
import org.http4k.filter.ResponseFilters
import org.http4k.filter.ServerFilters
import org.http4k.routing.RoutingHttpHandler
import org.http4k.routing.routes
import org.http4k.server.Jetty
import org.http4k.server.asServer
import java.util.concurrent.TimeUnit
import kotlin.reflect.KProperty1
import kotlin.reflect.full.memberProperties

const val OPENAPI_DOCS_PATH = "/docs/openapi.json"

fun http4kInJetty(
    apiInfo: String,
    production: Boolean,
    isLocalHost: Boolean,
    controllers: List<Any>,
) {
    val contractRoutes = controllers.flatMap { controller ->
        // extract ContractRoute fields from given Controller
        controller::class.memberProperties
            .filter { ContractRoute::class == it.returnType.classifier }
            .map { it as KProperty1<Any, ContractRoute> }
            .map {
                it.get(controller)
            }
    }

    http4kInJettyContracts(apiInfo, production, isLocalHost, contractRoutes)
}

fun http4kInJettyContracts(
    apiInfo: String,
    production: Boolean,
    isLocalHost: Boolean,
    routes: List<ContractRoute>,
    publicOpenAPI: Boolean = false,
) {
    initializeSentry(SystemEnv.environment)
    return jetty(
        production,
        isLocalHost,
        contract {
            // assert that SERVICE_NAME is defined (or die)
            systemEnv("SERVICE_NAME")

            this.renderer = OpenApi3(apiInfo = ApiInfo(apiInfo, "1.0"), json = CustomJacksonWithoutNulls)
            this.descriptionPath = OPENAPI_DOCS_PATH
            this.descriptionSecurity = object : Security {
                override val filter: Filter = Filter { next ->
                    {
                        if (publicOpenAPI) {
                            next(it)
                        } else if (!isLocalHost && it.parseJwtUser()?.roleIndex != 1) {
                            throw ForbiddenException()
                        } else {
                            next(it)
                        }
                    }
                }
            }

            this.routes += routes
        },
    )
}

private fun mutator(
    builder: SpanBuilder,
    req: Request,
): SpanBuilder =
    builder.setAllAttributes(
        Attributes.of(
            AttributeKey.stringKey("user-id"),
            req.parseJwtUser()?.id ?: "unauthenticated",
            AttributeKey.stringKey("user-agent"),
            req.userAgent ?: "undefined",
        ),
    )

fun jetty(
    production: Boolean,
    isLocalHost: Boolean,
    contractRoutes: RoutingHttpHandler,
) = cors
    .then(ServerFilters.OpenTelemetryTracing(spanCreationMutator = ::mutator))
    .then(onRequest(production, isLocalHost))
    .then(ResponseFilters.GZip())
    // necessary to attach custom response to 404s
    .then(notFoundThrower())
    // register routes
    .then(
        routes(
            static(),
            swaggerUi(OPENAPI_DOCS_PATH),
            contractRoutes,
        ).also {
            // unfortunately this seems to be the only way to test correctness of the openapi docs
            try {
                it(Request(Method.GET, "/api-docs"))
            } catch (e: NoFieldFound) {
                if (isLocalHost) {
                    // hard failure only locally
                    throw IllegalStateException("Cannot initialize OpenApi spec: ${e.message}", e)
                }
                log.fatal("Documentation is invalid: ${e.message}")
            }
        },
    )
    // we use Jetty as Netty may have some performance issues https://github.com/http4k/http4k/issues/141
    .asServer(Jetty(systemEnvRelaxed("SERVER_PORT")?.toInt() ?: SERVER_PORT))
    .start()
    .block()

private const val SERVER_PORT = 8080

// taken from https://www.cfnine.com/blog/opentelemetry-http4k/
@Suppress("Unused")
object Telemetry {
    private val otelSdk: OpenTelemetry

    init {
        otelSdk = buildOpenTelemetry()
    }

    private fun buildOpenTelemetry(): OpenTelemetry {

        val sdk: OpenTelemetrySdk = OpenTelemetrySdk.builder()
            // we must be using B3, because google cloud trace injects Traceparent header which fucks up tracing
            // for more info see https://lynn.zone/blog/opting-out-of-tracing-on-gcp/
            // google cloud tracing also decides sampling, so without B3 some spans were dropped
            // https://anecdotes.dev/opentelemetry-on-google-cloud-unraveling-the-mystery-f61f044c18be?gi=a0293118e111
            .setPropagators(ContextPropagators.create(B3Propagator.injectingMultiHeaders()))
            .setTracerProvider(
                SdkTracerProvider.builder()
                    .addSpanProcessor(
                        BatchSpanProcessor.builder(OtlpGrpcSpanExporter.getDefault())
                            .setExporterTimeout(4, TimeUnit.SECONDS)
                            .build(),
                    )
                    .build(),
            )
            .buildAndRegisterGlobal()

        Runtime.getRuntime().addShutdownHook(Thread(sdk::close))

        return sdk
    }
}
