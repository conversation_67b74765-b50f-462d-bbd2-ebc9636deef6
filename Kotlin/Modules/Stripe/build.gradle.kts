plugins {
    id("hero.kotlin-conventions")
}

val projectModule: (String) -> String by extra
dependencies {
    api("com.stripe:stripe-java:_")
    api("com.apple.itunes.storekit:app-store-server-library:_")

    implementation(projectModule(":Modules:BaseUtils"))
    implementation(projectModule(":Modules:Exceptions"))
    implementation(projectModule(":Modules:Model"))
    implementation(projectModule(":Modules:GoogleCloud"))
    implementation(projectModule(":Modules:Jwt"))

    testImplementation(projectModule(":Modules:Testing"))
    // we need to be able to load Stripe credentials from Firestore
    testImplementation(projectModule(":Modules:GoogleCloud"))
}
