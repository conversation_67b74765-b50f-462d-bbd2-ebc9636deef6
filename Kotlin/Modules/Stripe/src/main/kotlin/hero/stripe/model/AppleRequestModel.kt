package hero.stripe.model

/** https://developer.apple.com/documentation/advancedcommerceapi/subscriptionrevokerequest */
data class AppleSubscriptionRevokeRequest(
    val refundReason: String,
    /** A Boolean value that indicates whether the App Store asks you for consumption data to help inform the refund decision. */
    val refundRiskingPreference: Boolean,
    val refundType: AppleRefundType,
    override val requestInfo: AppleRequestInfo,
    /** A three-letter code that represents the country or region associated with the App Store storefront. */
    override val storefront: String?,
) : AppstoreRequest

enum class AppleRefundType { FULL, PRORATED }

/** https://developer.apple.com/documentation/advancedcommerceapi/subscriptioncancelrequest */
data class AppleSubscriptionCancelRequest(
    val requestInfo: AppleRequestInfo,
    /** A three-letter code that represents the country or region associated with the App Store storefront. */
    val storeFront: String?,
)

/** https://developer.apple.com/documentation/advancedcommerceapi/requestinfo */
data class AppleRequestInfo(
    val requestReferenceId: String,
    // TODO @nik said this should not be there despite spec above
    // val appAccountToken: String? = null,
    // val consistencyToken: String? = null,
)

/** https://developer.apple.com/documentation/advancedcommerceapi/subscriptioncreaterequest */
data class SubscriptionCreateRequest(
    val operation: InAppOperation,
    override val requestInfo: AppleRequestInfo,
    override val storefront: String?,
    val version: String,
    // this must be String, so it is correctly serialized as uppercase
    val currency: String,
    val taxCode: String,
    val descriptors: Descriptors,
    val period: SubscriptionPeriod,
    val items: List<ProductItem>,
) : AppstoreRequest

interface AppstoreRequest {
    val storefront: String?
    val requestInfo: AppleRequestInfo
}

enum class InAppOperation {
    CREATE_SUBSCRIPTION,
}

/** Kotlin-simplified variant of com.apple.itunes.storekit.model.ResponseBodyV2 */
data class ResponseBodyV2(val signedPayload: String)

data class ApplePublicKey(
    val kty: String,
    val kid: String,
    val use: String,
    val alg: String,
    val n: String,
    val e: String,
)

data class ApplePublicKeys(
    val keys: List<ApplePublicKey>,
)

enum class SubscriptionPeriod {
    /** 1 week in duration. */
    P1W,
    /** 1 month in duration. */
    P1M,
    /** 2 months in duration. */
    P2M,
    /** 3 months in duration. */
    P3M,
    /** 6 months in duration. */
    P6M,
    /** 1 year in duration. */
    P1Y,
}

data class Descriptors(
    val displayName: String,
    val description: String,
)

data class AdvancedCommerceInfo(
    val requestReferenceId: String,
    val period: SubscriptionPeriod,
    val descriptors: Map<String, String>,
    val items: List<ProductItem>,
)

// https://developer.apple.com/documentation/advancedcommerceapi/subscriptioncreateitem
data class ProductItem(
    val SKU: String,
    val displayName: String,
    val description: String,
    // https://developer.apple.com/documentation/advancedcommerceapi/prices
    val price: Int,
)

data class SignedPayload(
    val signedPayload: String,
)
