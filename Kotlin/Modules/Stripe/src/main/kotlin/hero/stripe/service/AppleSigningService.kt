package hero.stripe.service

import com.apple.itunes.storekit.model.Environment
import com.apple.itunes.storekit.model.ResponseBodyV2DecodedPayload
import com.apple.itunes.storekit.verification.SignedDataVerifier
import com.github.kittinunf.fuel.httpGet
import hero.baseutils.fetch
import hero.baseutils.toBase64
import hero.jackson.toJson
import hero.jwt.classLoader
import hero.stripe.model.ApplePublicKeys
import hero.stripe.model.AppstoreRequest
import io.jsonwebtoken.Claims
import io.jsonwebtoken.Jws
import io.jsonwebtoken.JwtParser
import io.jsonwebtoken.Jwts
import java.security.KeyFactory
import java.security.PublicKey
import java.security.cert.CertificateFactory
import java.security.spec.PKCS8EncodedKeySpec
import java.security.spec.X509EncodedKeySpec
import java.time.Instant
import java.util.Base64

/**
 * Generate Apple P8 key here:
 *    https://appstoreconnect.apple.com/access/integrations/api/subs
 *    https://appstoreconnect.apple.com/access/integrations/api
 * Then export public `der`:
 *    openssl ec -in apple_in_app_key.p8 -pubout -outform DER -out apple_in_app_public.der
 * Then export private `der`:
 *    openssl pkcs8 -topk8 -nocrypt -in apple_in_app_key.p8 -inform PEM -outform DER -out apple_in_app_private.der
 *
 * TODO use https://github.com/apple/app-store-server-library-java for verifications
 */
class AppleSigningService(
    private val production: Boolean,
    // TODO if (production) 6477841374 else 6743796440,
    private val appId: Long = 6477841374,
    private val bundleId: String = "co.herohero.Herohero",
    private val keyId: String = "M6N8N6JYFA",
    private val issuerId: String = "406dba29-8f22-47d5-ab52-dcc6549915cc",
    private val environment: Environment = if (production) Environment.PRODUCTION else Environment.SANDBOX,
) {
    // TODO do we need this?
    private val applePublicKeys = "https://appleid.apple.com/auth/keys"
        .httpGet()
        .fetch<ApplePublicKeys>()

    private val rootCAs = setOf(
        classLoader.getResourceAsStream("AppleIncRootCertificate.cer"),
        classLoader.getResourceAsStream("AppleRootCA-G2.cer"),
        classLoader.getResourceAsStream("AppleRootCA-G3.cer"),
    )

    private val signedPayloadVerifier = SignedDataVerifier(rootCAs, bundleId, appId, environment, true)

    private val kf = KeyFactory.getInstance("EC")
    private val applePrivateKeyBytes = classLoader.getResourceAsStream("apple_in_app_private.der")?.readAllBytes()
        ?: error("Key file not found in resources")

    private val applePublicKeyBytes = classLoader.getResourceAsStream("apple_in_app_public.der")?.readAllBytes()
        ?: error("Key file not found in resources")

    private val privateKeySpec = PKCS8EncodedKeySpec(applePrivateKeyBytes)
    private val publicKeySpec = X509EncodedKeySpec(applePublicKeyBytes)

    private val applePrivateKey = kf.generatePrivate(privateKeySpec)
    private val applePublicKey = kf.generatePublic(publicKeySpec)

    /**
     * https://developer.apple.com/documentation/storekit/generating-jws-to-sign-app-store-requests
     */
    fun sign(payload: AppstoreRequest): String =
        Jwts.builder()
            .header().keyId(keyId).type("JWT").add("alg", "ES256")
            .and()
            .claim("iss", issuerId)
            .claim("bid", bundleId)
            .claim("iat", Instant.now().epochSecond)
            .claim("aud", "advanced-commerce-api")
            .claim("nonce", payload.hashCode())
            .claim("request", payload.toJson().toBase64())
            .signWith(applePrivateKey)
            .compact()

    fun decodeNotification(signedPayload: String): ResponseBodyV2DecodedPayload =
        signedPayloadVerifier.verifyAndDecodeNotification(signedPayload)

    /** https://developer.apple.com/documentation/appstoreserverapi/jwstransactiondecodedpayload */
    fun parseX5cPayload(jwt: String): Jws<Claims> {
        // Step 1: Parse JWT header to extract x5c
        val parts = jwt.split(".")
        if (parts.size != 3) error("Invalid JWT format")

        val headerJson = String(Base64.getUrlDecoder().decode(parts[0]))
        val x5cRegex = Regex(""""x5c"\s*:\s*\[\s*"([^"]+)"""")
        val match = x5cRegex.find(headerJson)
            ?: error("x5c certificate not found in JWT header")

        val certBase64 = match.groupValues[1]

        // Step 2: Convert x5c to PublicKey
        val certBytes = Base64.getDecoder().decode(certBase64)
        val certFactory = CertificateFactory.getInstance("X.509")
        val cert = certFactory.generateCertificate(certBytes.inputStream())
        val publicKey: PublicKey = cert.publicKey

        // Step 3: Use the public key to validate the JWT
        val jwtParser: JwtParser = Jwts.parser()
            .verifyWith(publicKey)
            .build()

        return jwtParser.parseSignedClaims(jwt)
    }
}
