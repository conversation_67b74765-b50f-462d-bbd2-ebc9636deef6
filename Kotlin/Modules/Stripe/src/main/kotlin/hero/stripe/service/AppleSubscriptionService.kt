package hero.stripe.service

import com.github.kittinunf.fuel.core.Response
import com.github.kittinunf.fuel.core.extensions.cUrlString
import com.github.kittinunf.fuel.httpPost
import hero.jackson.toJson
import hero.stripe.model.AppleRefundType
import hero.stripe.model.AppleRequestInfo
import hero.stripe.model.AppleSubscriptionCancelRequest
import hero.stripe.model.AppleSubscriptionRevokeRequest

class AppleSubscriptionService(
    private val signingService: AppleSigningService,
) {
    // Immediate cancel and refund.
    // https://developer.apple.com/documentation/advancedcommerceapi/revoke-subscription
    fun cancel(
        appleTransactionId: String,
        appleReferenceId: String,
        refundReason: String,
    ): Response {
        val payload = AppleSubscriptionRevokeRequest(
            refundReason = refundReason,
            refundRiskingPreference = true,
            refundType = AppleRefundType.FULL,
            requestInfo = AppleRequestInfo(requestReferenceId = appleReferenceId),
            storefront = null,
        )
        val signature = signingService.sign(payload)

        return "https://api.storekit.itunes.apple.com/advancedCommerce/v1/subscription/revoke/$appleTransactionId"
            .httpPost()
            .header("Authorization", "Bearer $signature")
            .header("Content-Type", "application/json")
            .body(payload.toJson())
            .also { println(it.cUrlString()) }
            .response()
            .second
    }

    // Cancel at period end.
    // https://developer.apple.com/documentation/advancedcommerceapi/cancel-a-subscription
    fun cancelAtPeriodEnd(
        appleTransactionId: String,
        appleReferenceId: String,
    ) {
        // signing app store requests https://developer.apple.com/documentation/storekit/generating-jws-to-sign-app-store-requests
        "https://api.storekit.itunes.apple.com/advancedCommerce/v1/subscription/cancel/$appleTransactionId"
            .httpPost()
            .header("Content-Type", "application/json")
            .body(
                AppleSubscriptionCancelRequest(
                    requestInfo = AppleRequestInfo(requestReferenceId = appleReferenceId),
                    storeFront = null,
                ).toJson(),
            )
    }
}

fun main() {
    val service = AppleSubscriptionService(AppleSigningService(false))
    val respo = service.cancel("2000000938051915", "4f8402b0-9baf-4797-adb2-dedd4fcc42d0", "please")
    println(respo)
}
