import { ServiceDataSource } from './ServiceDataSource'
import { Environment } from '../common/environment'
import { DataSourceConfig } from '@apollo/datasource-rest'
import { CategoryModel, UserDetailsModel, UserModel } from '../models/user'
import {
    Creator,
    Direction,
    FeedUrlResponse,
    PagedUserResponse,
    SearchUsersRequest,
    UserDetailsResponse,
    UserDetailsUpdateRequest,
    UserDtoListResponse,
    UserResponse,
    UserStatus,
    ValidationErrorType,
    ValidationExceptionBodyError,
} from '../generated/api'
import { mapRole, mapTier, mapToUser } from './common-mappers'
import { PaginationParams, paginationParamsToQueryParams } from './common-utils'
import { notEmpty } from '../common/util'
import { TierModel } from '../models/subscription'
import {
    Currency,
    FeaturedCategories,
    RssFeedUrl,
    UserDetailsUpdateError,
    UserDetailsUpdateErrorType,
    UserProfileType,
} from '../generated/resolvers-types'
import { GraphQLError } from 'graphql/error'
import { getBodyFromExtensions, getCodeFromExtensions } from '../resolvers/utils'

export class UserAPI extends ServiceDataSource {
    private readonly internalApiKey: string

    constructor(internalApiKey: string, environment: Environment, cookies?: string, config?: DataSourceConfig) {
        super(environment, 'api', cookies, config)
        this.internalApiKey = internalApiKey
    }

    async updateUsersDetails(
        id: string,
        request: UserDetailsUpdateRequest
    ): Promise<{
        userDetails?: UserDetailsModel
        errors?: UserDetailsUpdateError[]
    }> {
        try {
            const userResponse = await this.put<UserDetailsResponse>(`/v4/users/${id}/details`, {
                body: request,
            })

            return {
                userDetails: this.mapToUserDetails(userResponse),
            }
        } catch (err) {
            if (err instanceof GraphQLError) {
                const code = getCodeFromExtensions(err.extensions)
                const body = getBodyFromExtensions(err.extensions) as ValidationExceptionBodyError[] | undefined
                if (code === 400 && body) {
                    return {
                        errors: body.map((e) => ({
                            property: e.property,
                            errorType: this.mapValidationError(e.error),
                            value: e.value,
                        })),
                    }
                }
            }

            throw err
        }
    }

    async getUser(id: string): Promise<UserModel> {
        const userResponse = await this.get<UserResponse>(`/v4/users/${id}`)
        return mapToUser(userResponse)
    }

    async getUserDetails(id: string): Promise<UserDetailsModel> {
        const userResponse = await this.get<UserDetailsResponse>(`/v4/users/${id}/details`)
        return this.mapToUserDetails(userResponse)
    }

    async getUserDetailsAsAdmin(id: string): Promise<UserDetailsModel> {
        const userResponse = await this.get<UserDetailsResponse>(`/admin/v1/users/${id}`)
        return this.mapToUserDetails(userResponse)
    }

    async searchUsers(query: string, paginationParams: PaginationParams): Promise<UserModel[]> {
        const request: SearchUsersRequest = {
            query,
            ...(paginationParams.first && {
                pageable: {
                    pageSize: paginationParams.first,
                    pageNumber: 0,
                    sort: {
                        direction: Direction.ASC,
                    },
                    offset: 0,
                },
            }),
        }

        return this.post<PagedUserResponse>(`/v4/users/search`, { body: request }).then((response) =>
            response.content.map(mapToUser)
        )
    }

    async searchUsersDeprecated(query: string, paginationParams: PaginationParams): Promise<UserModel[]> {
        const params = paginationParamsToQueryParams(paginationParams)
        params.append('query', query)
        const searchResponse = await this.get<UserDtoListResponse>(`/v2/users`, { params })

        return this.mapListResponse(searchResponse)
    }

    async featuredUsers(locale: string, paginationParams: PaginationParams): Promise<UserModel[]> {
        const params = paginationParamsToQueryParams(paginationParams)
        params.append('featuredBy', this.mapLocale(locale))
        const searchResponse = await this.get<UserDtoListResponse>(`/v2/users`, { params })

        return this.mapListResponse(searchResponse)
    }

    async featuredCreatorsRandomized(category: FeaturedCategories): Promise<UserModel[]> {
        const params = new URLSearchParams({
            pageSize: '100',
            type: category,
        })

        const response = await this.get<{ content: UserResponse[] }>(`/v1/featured-creators`, {
            params,
            headers: {
                'X-HeroHero-Api-Key': this.internalApiKey,
            },
        })

        return response.content
            .sort(() => Math.random() - 0.5)
            .slice(0, 20)
            .map((user) => mapToUser(user))
    }

    async rssFeedUrl(creatorId: string): Promise<RssFeedUrl> {
        const rssLinkResponse = await this.post<FeedUrlResponse>(`/v1/users/${creatorId}/rss-feed`)

        return {
            url: rssLinkResponse.url,
        }
    }

    private mapLocale(locale: string) {
        switch (locale) {
            case 'cs':
                return 'cs'
            case 'sk':
                return 'sk'
            case 'en':
                return 'en'
            case 'es':
                return 'es'
            default:
                return 'en'
        }
    }

    private mapToUserDetails(userResponse: UserDetailsResponse) {
        const userCreator = userResponse.creator
        const userDiscord = userResponse.discord

        return {
            id: userResponse.id,
            name: userResponse.name,
            bio: userResponse.bio,
            path: userResponse.path,
            subscribable: userResponse.subscribable,
            verified: userResponse.verified,
            ...(userResponse.image && {
                image: {
                    url: userResponse.image.id,
                    height: userResponse.image.height,
                    width: userResponse.image.width,
                    hidden: userResponse.image.hidden,
                },
            }),
            counts: {
                supporters: userResponse.counts.supporters,
                supporting: userResponse.counts.supporting,
                posts: userResponse.counts.posts,
                incomes: userResponse.counts.incomes,
                incomesClean: userResponse.counts.incomesClean,
                invoices: userResponse.counts.invoices,
                payments: userResponse.counts.payments,
            },
            hasRssFeed: userResponse.hasRssFeed,
            tier: mapTier(userResponse.tier),
            categories: userResponse.categories,
            email: userResponse.email ?? undefined,
            creator: this.mapCreator(userCreator),
            ...(userDiscord && {
                discord: {
                    id: userDiscord.id,
                    guildId: userDiscord.guildId,
                },
            }),
            notificationSettings: {
                emailNewPost: userResponse.notificationSettings.emailNewPost,
            },
            language: userResponse.language,
            role: mapRole(userResponse.role),
            spotify: {
                podcastUri: userResponse.spotify.spotifyUri,
                isConnected: userResponse.spotify.hasSpotifyConnection,
            },
            ...(userResponse.gjirafaLivestreamMeta && {
                livestream: {
                    streamUrl: userResponse.gjirafaLivestreamMeta.streamUrl,
                    streamKey: userResponse.gjirafaLivestreamMeta.streamKey,
                    publicId: userResponse.gjirafaLivestreamMeta.publicId,
                },
            }),
            isOfAge: userResponse.isOfAge,
            emailPublic: userResponse.creator?.emailPublic,
            emailInvoice: userResponse.creator?.emailInvoice,
        }
    }

    private mapCreator(creator?: Creator) {
        if (!creator?.stripeAccountId) {
            return undefined
        }

        return {
            stripeAccountId: creator.stripeAccountId,
            stripeAccountOnboarded: creator.stripeAccountOnboarded,
            stripeAccountActive: creator.stripeAccountActive,
        }
    }

    private mapListResponse(response: UserDtoListResponse) {
        return response.users.map((dto) => {
            const image = dto.attributes.image
            const categories = dto.relationships.categories ?? []

            const included = response.included
            const mappedCategories: CategoryModel[] = categories
                .map((category) => {
                    const includedCategory = included.categories.find((incCat) => incCat.id === category.id)

                    return includedCategory
                        ? {
                              id: includedCategory.id ?? '',
                              name: includedCategory.attributes.name,
                              slug: includedCategory.attributes.slug ?? '',
                          }
                        : null
                })
                .filter(notEmpty)

            return {
                id: dto.id,
                name: dto.attributes.name ?? '',
                bio: dto.attributes.bio ?? '',
                path: dto.attributes.path ?? '',
                subscribable: dto.attributes.subscribable,
                verified: dto.attributes.verified,
                ...(image && {
                    image: {
                        url: image.id,
                        height: image.height,
                        width: image.width,
                        hidden: image.hidden,
                    },
                }),
                counts: {
                    supporters: dto.attributes.counts.supporters,
                    supportersThreshold: dto.attributes.counts.supportersThreshold,
                    supporting: dto.attributes.counts.supporting,
                    posts: dto.attributes.counts.posts ?? 0,
                },
                hasRssFeed: dto.attributes.hasRssFeed,
                tier: this.parseTier(dto.relationships.tier?.id ?? ''),
                categories: mappedCategories,
                isDeleted: dto.attributes.status == UserStatus.DELETED,
                privacyPolicyEnabled: false,
                hasGiftsAllowed: dto.attributes.hasGiftsAllowed,
                profileType: UserProfileType.PUBLIC,
            }
        })
    }

    private parseTier(tierId: string): TierModel {
        const CURRENCY_LENGTH = 3
        const PRICE_LENGTH = 2
        return {
            id: tierId,
            currency: Currency[tierId.substring(0, CURRENCY_LENGTH) as keyof typeof Currency],
            priceCents: parseInt(tierId.substring(CURRENCY_LENGTH, CURRENCY_LENGTH + PRICE_LENGTH)) * 100,
            hidden: false,
            default: false,
        }
    }

    private mapValidationError(errorType: ValidationErrorType): UserDetailsUpdateErrorType {
        switch (errorType) {
            case ValidationErrorType.MIN_LENGTH_TWO:
                return UserDetailsUpdateErrorType.MIN_LENGTH_TWO
            case ValidationErrorType.LOWERCASE_ALPHANUMERIC:
                return UserDetailsUpdateErrorType.LOWERCASE_ALPHANUMERIC
            case ValidationErrorType.ILLEGAL_STRING:
                return UserDetailsUpdateErrorType.ILLEGAL_STRING
            case ValidationErrorType.NAME_TAKEN:
                return UserDetailsUpdateErrorType.PATH_TAKEN
            case ValidationErrorType.PATH_CHANGE_TOO_OFTEN:
                return UserDetailsUpdateErrorType.PATH_CHANGE_TOO_OFTEN
            case ValidationErrorType.MAX_LENGTH_EXCEEDED:
                return UserDetailsUpdateErrorType.MAX_LENGTH_EXCEEDED
        }
    }
}
