import nock from 'nock'
import { Environment } from '../../src/common/environment'
import { UserAPI } from '../../src/datasources/UserAPI'
import { UserDetailsModel, UserModel } from '../../src/models/user'
import {
    Currency as RestCurrency,
    Direction,
    FeedUrlResponse,
    Role,
    UserDetailsResponse,
    UserDtoListResponse,
    UserStatus,
    ValidationErrorType,
} from '../../src/generated/api'
import { userResponse } from './test-utils'
import {
    Currency,
    FeaturedCategories,
    UserDetailsUpdateErrorType,
    UserProfileType,
    UserRole,
} from '../../src/generated/resolvers-types'

beforeEach(() => {
    nock.disableNetConnect()
})

describe('data source: UserAPI', () => {
    describe('method: updateUsersDetails', () => {
        test("should make a call to update user's details and map successful response", async () => {
            // given
            const underTest = new UserAPI('internal-api-key', Environment.DEVEL)
            const userId = 'user-id'
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .put(`/v4/users/${userId}/details`, {
                    path: 'path',
                    bio: 'bio',
                    name: 'name',
                    profileImage: {
                        url: 'image-url',
                        width: 500,
                        height: 600,
                    },
                    isOfAge: true,
                })
                .reply(200, userDetailsResponse(userId))

            // when
            const userModel = await underTest.updateUsersDetails(userId, {
                path: 'path',
                bio: 'bio',
                name: 'name',
                profileImage: {
                    url: 'image-url',
                    width: 500,
                    height: 600,
                },
                isOfAge: true,
            })

            // then
            expect(userModel).toEqual({
                userDetails: {
                    id: userId,
                    name: 'user-name',
                    bio: 'user-bio',
                    image: {
                        url: 'image-id',
                        width: 420,
                        height: 69,
                        hidden: true,
                    },
                    path: 'user-path',
                    subscribable: true,
                    verified: false,
                    counts: {
                        supporting: 150,
                        supporters: 21231,
                        posts: 243,
                        payments: 300,
                        invoices: 121,
                        incomesClean: 421,
                        incomes: 21,
                    },
                    hasRssFeed: true,
                    tier: {
                        id: 'EUR05',
                        priceCents: 500,
                        hidden: false,
                        default: false,
                        currency: Currency.EUR,
                    },
                    categories: [
                        {
                            id: 'category-id',
                            name: 'category-name',
                            slug: 'slug',
                        },
                    ],
                    language: 'cs',
                    discord: {
                        id: 'discord-id',
                        guildId: 'guild-id',
                    },
                    creator: {
                        stripeAccountId: 'acct_1OK1c9Pn4ygREaGP',
                        stripeAccountActive: true,
                        stripeAccountOnboarded: false,
                    },
                    email: 'user-email',
                    notificationSettings: {
                        emailNewPost: false,
                    },
                    role: UserRole.MODERATOR,
                    spotify: {
                        podcastUri: 'spotify-uri',
                        isConnected: true,
                    },
                    livestream: {
                        streamKey: 'stream-key',
                        streamUrl: 'stream-url',
                        publicId: 'public-id',
                    },
                    isOfAge: true,
                    emailPublic: '<EMAIL>',
                    emailInvoice: '<EMAIL>',
                },
            })
            expect(scope.isDone()).toBeTruthy()
        })

        test("should make a call to get to update user's details and map 400 response", async () => {
            // given
            const underTest = new UserAPI('internal-api-key', Environment.DEVEL)
            const userId = 'user-id'
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .put(`/v4/users/${userId}/details`, {
                    path: 'path',
                    bio: 'bio',
                    name: 'name',
                })
                .reply(400, [{ property: 'name', value: 'hh', error: ValidationErrorType.MIN_LENGTH_TWO }])

            // when
            const userModel = await underTest.updateUsersDetails(userId, { path: 'path', bio: 'bio', name: 'name' })

            // then
            expect(userModel).toEqual({
                errors: [
                    {
                        property: 'name',
                        value: 'hh',
                        errorType: UserDetailsUpdateErrorType.MIN_LENGTH_TWO,
                    },
                ],
            })
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: getUser', () => {
        test("should make a call to get a user's public info and map it", async () => {
            // given
            const underTest = new UserAPI('internal-api-key', Environment.DEVEL)
            const userId = 'user-id'
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v4/users/${userId}`)
                .reply(200, userResponse(userId))

            // when
            const userModel = await underTest.getUser(userId)

            // then
            expect(userModel).toEqual<UserModel>({
                id: userId,
                name: 'user-name',
                bio: 'user-bio',
                image: {
                    url: 'image-id',
                    width: 420,
                    height: 69,
                    hidden: true,
                },
                path: 'user-path',
                subscribable: true,
                verified: false,
                counts: {
                    supporting: 150,
                    supporters: 21231,
                    supportersThreshold: 25000,
                    posts: 243,
                },
                spotifyShowUri: 'spotify-uri',
                hasRssFeed: true,
                tier: {
                    id: 'EUR05',
                    priceCents: 500,
                    hidden: false,
                    default: false,
                    currency: Currency.EUR,
                },
                categories: [
                    {
                        id: 'category-id',
                        name: 'category-name',
                        slug: 'slug',
                    },
                ],
                isDeleted: false,
                privacyPolicyEnabled: false,
                analytics: {
                    facebookPixelId: 'facebook-pixel-id',
                },
                hasGiftsAllowed: true,
                emailPublic: '<EMAIL>',
                profileType: UserProfileType.PUBLIC,
            })
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: getUserDetails', () => {
        test("should make a call to get a user's details and map it", async () => {
            // given
            const underTest = new UserAPI('internal-api-key', Environment.DEVEL)
            const userId = 'user-id'
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v4/users/${userId}/details`)
                .reply(200, userDetailsResponse(userId))

            // when
            const userModel = await underTest.getUserDetails(userId)

            // then
            expect(userModel).toEqual<UserDetailsModel>({
                id: userId,
                name: 'user-name',
                bio: 'user-bio',
                image: {
                    url: 'image-id',
                    width: 420,
                    height: 69,
                    hidden: true,
                },
                path: 'user-path',
                subscribable: true,
                verified: false,
                counts: {
                    supporting: 150,
                    supporters: 21231,
                    posts: 243,
                    payments: 300,
                    invoices: 121,
                    incomesClean: 421,
                    incomes: 21,
                },
                hasRssFeed: true,
                tier: {
                    id: 'EUR05',
                    priceCents: 500,
                    hidden: false,
                    default: false,
                    currency: Currency.EUR,
                },
                categories: [
                    {
                        id: 'category-id',
                        name: 'category-name',
                        slug: 'slug',
                    },
                ],
                language: 'cs',
                discord: {
                    id: 'discord-id',
                    guildId: 'guild-id',
                },
                creator: {
                    stripeAccountActive: true,
                    stripeAccountOnboarded: false,
                    stripeAccountId: 'acct_1OK1c9Pn4ygREaGP',
                },
                email: 'user-email',
                notificationSettings: {
                    emailNewPost: false,
                },
                role: UserRole.MODERATOR,
                spotify: {
                    podcastUri: 'spotify-uri',
                    isConnected: true,
                },
                livestream: {
                    streamKey: 'stream-key',
                    streamUrl: 'stream-url',
                    publicId: 'public-id',
                },
                isOfAge: true,
                emailPublic: '<EMAIL>',
                emailInvoice: '<EMAIL>',
            })
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: getUserDetailsAsAdmin', () => {
        test("should make a call to get a user's details and map it", async () => {
            // given
            const underTest = new UserAPI('internal-api-key', Environment.DEVEL)
            const userId = 'user-id'
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/admin/v1/users/${userId}`)
                .reply(200, userDetailsResponse(userId))

            // when
            const userModel = await underTest.getUserDetailsAsAdmin(userId)

            // then
            expect(userModel).toEqual<UserDetailsModel>({
                id: userId,
                name: 'user-name',
                bio: 'user-bio',
                image: {
                    url: 'image-id',
                    width: 420,
                    height: 69,
                    hidden: true,
                },
                path: 'user-path',
                subscribable: true,
                verified: false,
                counts: {
                    supporting: 150,
                    supporters: 21231,
                    posts: 243,
                    payments: 300,
                    invoices: 121,
                    incomesClean: 421,
                    incomes: 21,
                },
                hasRssFeed: true,
                tier: {
                    id: 'EUR05',
                    priceCents: 500,
                    hidden: false,
                    default: false,
                    currency: Currency.EUR,
                },
                categories: [
                    {
                        id: 'category-id',
                        name: 'category-name',
                        slug: 'slug',
                    },
                ],
                language: 'cs',
                discord: {
                    id: 'discord-id',
                    guildId: 'guild-id',
                },
                creator: {
                    stripeAccountActive: true,
                    stripeAccountOnboarded: false,
                    stripeAccountId: 'acct_1OK1c9Pn4ygREaGP',
                },
                email: 'user-email',
                notificationSettings: {
                    emailNewPost: false,
                },
                role: UserRole.MODERATOR,
                spotify: {
                    podcastUri: 'spotify-uri',
                    isConnected: true,
                },
                livestream: {
                    streamKey: 'stream-key',
                    streamUrl: 'stream-url',
                    publicId: 'public-id',
                },
                isOfAge: true,
                emailPublic: '<EMAIL>',
                emailInvoice: '<EMAIL>',
            })
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: searchUser', () => {
        test('should make a call to search users and then map them', async () => {
            // given
            const underTest = new UserAPI('internal-api-key', Environment.DEVEL)
            const userId = 'user-id'
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .post(`/v4/users/search`, {
                    query: 'query',
                    pageable: {
                        pageSize: 10,
                        pageNumber: 0,
                        sort: {
                            direction: Direction.ASC,
                        },
                        offset: 0,
                    },
                })
                .reply(200, { content: [userResponse(userId)] })

            // when
            const userModel = await underTest.searchUsers('query', { first: 10 })

            // then
            expect(userModel).toEqual<UserModel[]>([
                {
                    id: userId,
                    name: 'user-name',
                    bio: 'user-bio',
                    image: {
                        url: 'image-id',
                        width: 420,
                        height: 69,
                        hidden: true,
                    },
                    path: 'user-path',
                    subscribable: true,
                    verified: false,
                    spotifyShowUri: 'spotify-uri',
                    counts: {
                        supporting: 150,
                        supporters: 21231,
                        supportersThreshold: 25000,
                        posts: 243,
                    },
                    hasRssFeed: true,
                    tier: {
                        id: 'EUR05',
                        priceCents: 500,
                        hidden: false,
                        default: false,
                        currency: Currency.EUR,
                    },
                    categories: [
                        {
                            id: 'category-id',
                            name: 'category-name',
                            slug: 'slug',
                        },
                    ],
                    isDeleted: false,
                    privacyPolicyEnabled: false,
                    analytics: {
                        facebookPixelId: 'facebook-pixel-id',
                    },
                    hasGiftsAllowed: true,
                    emailPublic: '<EMAIL>',
                    profileType: UserProfileType.PUBLIC,
                },
            ])
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: searchUsersDeprecated', () => {
        test('should make a call to search users and map them', async () => {
            // given
            const underTest = new UserAPI('internal-api-key', Environment.DEVEL)
            const userId = 'user-id'
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v2/users`)
                .query({ pageSize: 10, query: 'vojt' })
                .reply(200, searchUsersResponse())

            // when
            const users = await underTest.searchUsersDeprecated('vojt', { first: 10 })

            // then
            expect(users).toEqual<UserModel[]>([
                {
                    id: userId,
                    name: 'user-name',
                    bio: 'user-bio',
                    image: {
                        url: 'image-id',
                        width: 420,
                        height: 69,
                        hidden: true,
                    },
                    path: 'user-path',
                    subscribable: true,
                    verified: false,
                    counts: {
                        supporting: 100,
                        supporters: 300,
                        supportersThreshold: 500,
                        posts: 200,
                    },
                    hasRssFeed: true,
                    tier: {
                        id: 'EUR05',
                        priceCents: 500,
                        hidden: false,
                        default: false,
                        currency: Currency.EUR,
                    },
                    categories: [
                        {
                            id: 'category-id',
                            name: 'category-name',
                            slug: 'slug',
                        },
                    ],
                    isDeleted: false,
                    privacyPolicyEnabled: false,
                    hasGiftsAllowed: true,
                    profileType: UserProfileType.PUBLIC,
                },
            ])
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: featuredUsers', () => {
        test('should make a call to get featured users and map them', async () => {
            // given
            const underTest = new UserAPI('internal-api-key', Environment.DEVEL)
            const userId = 'user-id'
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v2/users`)
                .query({ pageSize: 10, featuredBy: 'en' })
                .reply(200, searchUsersResponse())

            // when
            const users = await underTest.featuredUsers('en', { first: 10 })

            // then
            expect(users).toEqual<UserModel[]>([
                {
                    id: userId,
                    name: 'user-name',
                    bio: 'user-bio',
                    image: {
                        url: 'image-id',
                        width: 420,
                        height: 69,
                        hidden: true,
                    },
                    path: 'user-path',
                    subscribable: true,
                    verified: false,
                    counts: {
                        supporting: 100,
                        supporters: 300,
                        supportersThreshold: 500,
                        posts: 200,
                    },
                    hasRssFeed: true,
                    tier: {
                        id: 'EUR05',
                        priceCents: 500,
                        hidden: false,
                        default: false,
                        currency: Currency.EUR,
                    },
                    categories: [
                        {
                            id: 'category-id',
                            name: 'category-name',
                            slug: 'slug',
                        },
                    ],
                    isDeleted: false,
                    privacyPolicyEnabled: false,
                    hasGiftsAllowed: true,
                    profileType: UserProfileType.PUBLIC,
                },
            ])
            expect(scope.isDone()).toBeTruthy()
        })

        test('fr locale should default to en', async () => {
            // given
            const underTest = new UserAPI('internal-api-key', Environment.DEVEL)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v2/users`)
                .query({ pageSize: 10, featuredBy: 'en' })
                .reply(200, searchUsersResponse())

            // when
            await underTest.featuredUsers('fr', { first: 10 })

            // then
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: featuredCreatorsRandomized', () => {
        test('should make a call to get featured creators, randomize and pick 20, and map it', async () => {
            // given
            const underTest = new UserAPI('internal-api-key', Environment.DEVEL)
            const userId = 'user-id'
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v1/featured-creators?pageSize=100&type=NEW_CREATORS`)
                .matchHeader('X-HeroHero-Api-Key', 'internal-api-key')
                .reply(200, { content: [userResponse(userId)] })

            // when
            const users = await underTest.featuredCreatorsRandomized(FeaturedCategories.NEW_CREATORS)

            // then
            expect(users).toEqual<UserModel[]>([
                {
                    id: userId,
                    name: 'user-name',
                    bio: 'user-bio',
                    image: {
                        url: 'image-id',
                        width: 420,
                        height: 69,
                        hidden: true,
                    },
                    path: 'user-path',
                    subscribable: true,
                    verified: false,
                    counts: {
                        supporting: 150,
                        supporters: 21231,
                        supportersThreshold: 25000,
                        posts: 243,
                    },
                    spotifyShowUri: 'spotify-uri',
                    hasRssFeed: true,
                    tier: {
                        id: 'EUR05',
                        priceCents: 500,
                        hidden: false,
                        default: false,
                        currency: Currency.EUR,
                    },
                    categories: [
                        {
                            id: 'category-id',
                            name: 'category-name',
                            slug: 'slug',
                        },
                    ],
                    isDeleted: false,
                    privacyPolicyEnabled: false,
                    analytics: {
                        facebookPixelId: 'facebook-pixel-id',
                    },
                    hasGiftsAllowed: true,
                    emailPublic: '<EMAIL>',
                    profileType: UserProfileType.PUBLIC,
                },
            ])
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: rssFeed', () => {
        test('should make a call to generate rss feed url', async () => {
            // given
            const underTest = new UserAPI('internal-api-key', Environment.DEVEL)
            const response: FeedUrlResponse = {
                url: 'feed-url',
            }
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .post(`/v1/users/cestmir/rss-feed`)
                .reply(200, response)

            // when
            const rssFeedUrl = await underTest.rssFeedUrl('cestmir')

            expect(rssFeedUrl).toEqual({ url: 'feed-url' })
            expect(scope.isDone()).toBeTruthy()
        })
    })
})

function searchUsersResponse(): UserDtoListResponse {
    return {
        users: [
            {
                id: 'user-id',
                type: 'user',
                attributes: {
                    hasSpotifyConnection: false,
                    hasLivestreams: false,
                    spotifyFeedReady: false,
                    hasSpotifyExport: false,
                    hasDrm: false,
                    status: UserStatus.ACTIVE,
                    image: {
                        id: 'image-id',
                        height: 69,
                        width: 420,
                        hidden: true,
                    },
                    bio: 'user-bio',
                    name: 'user-name',
                    counts: {
                        supporting: 100,
                        supporters: 300,
                        supportersThreshold: 500,
                        posts: 200,
                    },
                    path: 'user-path',
                    hasRssFeed: true,
                    subscribable: true,
                    verified: false,
                    analytics: {
                        facebookPixelId: '',
                    },
                    hasGiftsAllowed: true,
                },
                relationships: {
                    categories: [{ id: 'category-id', type: 'category' }],
                    tier: {
                        type: 'tier',
                        id: 'EUR05',
                    },
                },
            },
        ],
        included: {
            categories: [
                {
                    id: 'category-id',
                    type: 'category',
                    attributes: {
                        slug: 'slug',
                        name: 'category-name',
                        postCount: 100,
                    },
                    relationships: {
                        user: {
                            type: 'user',
                            id: 'user-id',
                        },
                    },
                },
                {
                    id: 'another-category-id',
                    type: 'category',
                    attributes: {
                        slug: 'another-slug',
                        name: 'another-category',
                        postCount: 100,
                    },
                    relationships: {
                        user: {
                            type: 'user',
                            id: 'another-user',
                        },
                    },
                },
            ],
            tiers: [],
        },
        meta: {
            pageIndex: 10,
            hasNext: false,
        },
    }
}

function userDetailsResponse(userId: string): UserDetailsResponse {
    return {
        id: userId,
        name: 'user-name',
        bio: 'user-bio',
        bioHtml: '<h1>user-bio</h1>',
        bioEn: 'user-bio-en',
        bioHtmlEn: '<h1>user-bio-en</h1>',
        image: {
            id: 'image-id',
            width: 420,
            height: 69,
            hidden: true,
        },
        path: 'user-path',
        subscribable: true,
        verified: false,
        counts: {
            supporting: 150,
            supporters: 21231,
            posts: 243,
            payments: 300,
            invoices: 121,
            incomesClean: 421,
            incomes: 21,
        },
        hasRssFeed: true,
        tier: {
            id: 'EUR05',
            priceCents: 500,
            feeCents: 10,
            hidden: false,
            default: false,
            currency: RestCurrency.EUR,
        },
        categories: [
            {
                id: 'category-id',
                name: 'category-name',
                slug: 'slug',
            },
        ],
        language: 'cs',
        discord: {
            id: 'discord-id',
            guildId: 'guild-id',
        },
        creator: {
            stripeAccountId: 'acct_1OK1c9Pn4ygREaGP',
            stripeAccountActive: true,
            stripeAccountOnboarded: false,
            tierId: 'EUR05',
            suspended: false,
            active: true,
            verified: true,
            stripeAccountLegacyIds: [],
            emailPublic: '<EMAIL>',
            emailInvoice: '<EMAIL>',
        },
        email: 'user-email',
        notificationSettings: {
            emailNewPost: false,
            emailNewDm: false,
            newsletter: false,
            termsChanged: false,
            pushNewComment: false,
            pushNewPost: false,
        },
        role: Role.MODERATOR,
        analytics: {
            facebookPixelId: 'facebook-pixel-id',
        },
        spotify: {
            spotifyUri: 'spotify-uri',
            hasSpotifyConnection: true,
        },
        gjirafaLivestreamMeta: {
            streamId: 'stream-id',
            streamUrl: 'stream-url',
            streamKey: 'stream-key',
            playbackUrl: 'playback-url',
            publicId: 'public-id',
        },
        isOfAge: true,
    }
}
